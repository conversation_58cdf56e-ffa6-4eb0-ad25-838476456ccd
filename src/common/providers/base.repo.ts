import { Transaction } from 'sequelize';
import { BaseService } from '@/common/providers/base.service';
import logger from '@/modules/logger/logger.service';

export abstract class BaseRepository<T> extends BaseService<T> {
    private tx: Transaction | null;
    constructor(private tag: string) {
        super();
        this.tag = tag;
    }

    log(...msg: string[]) {
        logger.info(`${this.tag} ${msg.join(' ')}`);
    }

    get transaction(): Transaction | null {
        const status = (this.tx as any)?.finished;
        if (status == 'commit' || status == 'rollback') this.tx = null;
        return this.tx;
    }

    set transaction(tx: Transaction | null) {
        this.tx = tx;
    }
}
