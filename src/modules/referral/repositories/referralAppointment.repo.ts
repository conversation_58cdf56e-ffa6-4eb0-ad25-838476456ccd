import db, { ReferralAppointment } from '@clinico/clinico-persistence';
import { EnumReferralType } from '@clinico/clinico-persistence/dist/models/referralSocialUser.model';
import * as DataLoader from 'dataloader';
import { groupBy } from 'lodash';
import * as moment from 'moment';
import { WhereOptions } from 'sequelize';
import { Service } from 'typedi';
import { DataLoaderBaseService } from '@/common/providers/dataLoaderBase.service';
import { FindReferralByChannelUserIdParams } from '../types/referral.type';
import {
    CreateReferralAppointmentParams,
    FindOrCreateReferralAppointmentByChannelUserIdParams,
    FindOrCreateReferralAppointmentByMobileParams,
    SearchReferralAppointmentParams,
    UpdateReferralAppointmentParams,
} from '../types/referralAppointment.type';

@Service()
export class ReferralAppointmentRepository extends DataLoaderBaseService<ReferralAppointment> {
    async existByAppointmentId(
        appointmentId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ) {
        const { rows } = await this.search({
            appointmentId,
            referralType,
        });
        return rows.length > 0;
    }

    async update(params: UpdateReferralAppointmentParams) {
        const data = await this.findOneOrError(params.id);
        for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
                data[key] = value;
            }
        }
        return data.save();
    }

    async bulkDelete(ids: number[]) {
        return db.ReferralAppointment.destroy({
            where: {
                id: {
                    [db.Op.in]: ids,
                },
            },
        });
    }

    async findOrCreateByMobile(
        params: FindOrCreateReferralAppointmentByMobileParams,
    ) {
        const [data] = await db.ReferralAppointment.findOrCreate({
            where: {
                companyId: params.companyId,
                appointmentId: params.appointmentId,
                mobile: params.mobile,
            },
            defaults: {
                ...params,
            },
        });
        return data;
    }

    async findOrCreateUpdateByMobile(
        params: FindOrCreateReferralAppointmentByMobileParams,
    ) {
        const data = await this.findOrCreateByMobile(params);
        for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
                data[key] = value;
            }
        }
        return data.save();
    }

    async findOrCreatUpdateByChannelUserId(
        params: FindOrCreateReferralAppointmentByChannelUserIdParams,
    ) {
        const data = await this.findOrCreatByChannelUserId(params);
        for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
                data[key] = value;
            }
        }
        return data.save();
    }

    async findOrCreatByChannelUserId(
        params: FindOrCreateReferralAppointmentByChannelUserIdParams,
    ) {
        const [data] = await db.ReferralAppointment.findOrCreate({
            where: {
                appointmentId: params.appointmentId,
                referralType: params.referralType,
                channelUserId: params.channelUserId,
            },
            defaults: {
                ...params,
                referralDate: moment().format('YYYY-MM-DD'),
            },
        });
        return data;
    }

    async findOrCreate(
        params: FindOrCreateReferralAppointmentByChannelUserIdParams,
    ) {
        const [data] = await db.ReferralAppointment.findOrCreate({
            where: {
                appointmentId: params.appointmentId,
                referralType: params.referralType,
            },
            defaults: {
                ...params,
                referralDate: moment().format('YYYY-MM-DD'),
            },
        });
        return data;
    }

    async create(params: CreateReferralAppointmentParams) {
        const data = await db.ReferralAppointment.create({
            ...params,
            referralDate: moment().format('YYYY-MM-DD'),
        });
        return data;
    }

    /**
     * 根據推薦類型，取得最新的預約推薦資料
     * @param appointmentId
     * @param referralType
     * @returns
     */
    async findLatestByAppointmentId(
        appointmentId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ) {
        const { rows } = await this.search({
            appointmentId,
            referralType,
            limit: 1,
        });
        return rows.length ? rows[0] : null;
    }

    /**
     * 根據推薦類型，從社群人員資訊取得最新的預約推薦資料
     * @param params
     * @param referralType
     * @returns
     */
    async findLatestByChannelUserIdParams(
        params: FindReferralByChannelUserIdParams,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ) {
        const { rows } = await this.search({
            ...params,
            referralType,
            limit: 1,
        });
        return rows.length ? rows[0] : null;
    }

    async search(params: SearchReferralAppointmentParams) {
        const filter: WhereOptions = {};
        filter.deleted = false;
        if (params.id) filter.id = params.id;
        if (params.ids) filter.id = { [db.Op.in]: params.ids };
        if (params.companyId) filter.companyId = params.companyId;
        if (params.appointmentId) filter.appointmentId = params.appointmentId;
        if (params.appointmentIds)
            filter.appointmentId = {
                [db.Op.in]: params.appointmentIds,
            };
        if (params.channelUserId) filter.channelUserId = params.channelUserId;
        if (params.socialUserId) filter.socialUserId = params.socialUserId;
        if (params.memberId) filter.memberId = params.memberId;
        if (params.mobile) filter.mobile = params.mobile;
        if (params.mobiles) filter.mobile = { [db.Op.in]: params.mobiles };
        if (params.referralCode) filter.referralCode = params.referralCode;
        if (params.referralCodes)
            filter.referralCode = {
                [db.Op.in]: params.referralCodes,
            };
        if (params.referralType) filter.referralType = params.referralType;
        if (params.referralDate) filter.referralDate = params.referralDate;
        const data = await db.ReferralAppointment.findAndCountAll({
            where: filter,
            order: [['updated_at', 'DESC']],
            limit: params.limit,
            offset: params.offset,
        });
        return data;
    }

    // Dataloader for finding latest referral appointment by appointment ID
    private findLatestByAppointmentIdDataloader = new DataLoader(
        async (appointmentIds: readonly number[]) => {
            const res = await db.ReferralAppointment.findAll({
                where: {
                    appointmentId: { [db.Op.in]: appointmentIds as number[] },
                    referralType: EnumReferralType.DOCTOR_REFERRAL,
                },
                order: [['updated_at', 'DESC']],
            });
            const grouped = groupBy(res, 'appointmentId');
            return appointmentIds.map((key) => grouped[key]?.[0] || null);
        },
        {
            cache: false,
            batchScheduleFn: (callback) => setTimeout(callback, 10),
        },
    );

    async findLatestByAppointmentIdUseDataloader(
        appointmentId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ): Promise<ReferralAppointment | null> {
        if (referralType !== EnumReferralType.DOCTOR_REFERRAL) {
            // Fallback to original method for non-doctor referrals
            return this.findLatestByAppointmentId(appointmentId, referralType);
        }
        return await this.findLatestByAppointmentIdDataloader.load(
            appointmentId,
        );
    }
}
