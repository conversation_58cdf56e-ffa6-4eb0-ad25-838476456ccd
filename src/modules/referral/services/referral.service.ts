import { EnumReferralType } from '@clinico/clinico-persistence/dist/models/referralSocialUser.model';
import { Inject, Service } from 'typedi';
import { AppointmentLibService } from '@/modules/appointment/services/appointment.lib.service';
import { SocialUserRepository } from '@/modules/socialUser/repositories/socialUser.repo';
import { SocialUserService } from '@/modules/socialUser/services/socialUser.service';
import { StoreService } from '@/modules/store/store.service';
import { WorkDiaryReferralService } from '@/modules/workDiary/workDiaryReferral.service';
import { MemberChannelIdService } from '../../member/memberChannelId.service';
import { ReferralAppointmentRepository } from '../repositories/referralAppointment.repo';
import { ReferralSocialUserRepository } from '../repositories/referralSocialUser.repo';
import { DoctorReferralInfo, ReferralInfo } from '../types/referral.gql.type';
import { FindReferralByChannelUserIdParams } from '../types/referral.type';
import { ReferralLibService } from './referral.lib.service';
import { ReferralMemberService } from './referralMember.service';

@Service()
export class ReferralService {
    @Inject()
    private referralSocialUserRepo: ReferralSocialUserRepository;
    @Inject()
    private referralAppointmentRepo: ReferralAppointmentRepository;
    @Inject()
    private socialUserRepo: SocialUserRepository;
    @Inject()
    private appointmentLibService: AppointmentLibService;
    @Inject()
    private memberChannelIdSvc: MemberChannelIdService;
    @Inject()
    private workDiaryReferralSvc: WorkDiaryReferralService;
    @Inject()
    private referralLibSvc: ReferralLibService;
    @Inject()
    private referralMemberSvc: ReferralMemberService;
    @Inject()
    private storeSvc: StoreService;
    @Inject()
    private socialUserSvc: SocialUserService;

    /**
     * 根據預約ID，取得預約的轉介資料
     * @param appointmentId
     * @param referralType
     * @returns
     */
    async getReferralAppointmentId(
        appointmentId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ): Promise<DoctorReferralInfo | null> {
        const referralAppointment =
            await this.referralAppointmentRepo.findLatestByAppointmentIdUseDataloader(
                appointmentId,
                referralType,
            );
        if (!referralAppointment) {
            return null;
        }
        return this.workDiaryReferralSvc.findOneByDoctorCode(
            referralAppointment.referralCode,
        );
    }

    /**
     * 從預約ID取得綜合的最新轉介資料
     * - 預約的轉介資料
     * - 預約有關聯的社群人員的轉介資料
     * @param appointmentId
     * @param referralType
     * @returns
     */
    async getLatestReferralByAppointmentId(
        appointmentId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ): Promise<ReferralInfo | null> {
        const referralData: ReferralInfo[] = [];
        const [referralAppointment, appointment] = await Promise.all([
            this.referralAppointmentRepo.findLatestByAppointmentId(
                appointmentId,
                referralType,
            ),
            this.appointmentLibService.findOneOrError(appointmentId),
        ]);
        if (referralAppointment) {
            referralData.push({
                companyId: referralAppointment.companyId,
                referralAppointmentId: referralAppointment.id,
                referralCode: referralAppointment.referralCode,
                referralDate: referralAppointment.referralDate,
                referralType: referralAppointment.referralType,
                appointmentId: referralAppointment.appointmentId,
                updatedAt: referralAppointment.updatedAt,
            });
        }
        const store = await this.storeSvc.findOneOrError(appointment.storeId);
        const companyId = appointment.store?.companyId ?? store.companyId;
        const memberId = appointment.memberId;

        if (memberId) {
            const socialUsers =
                await this.socialUserSvc.getFindOrCreateSocialUsersByMemberId(
                    companyId,
                    memberId,
                );
            const referralSocialUsers = await Promise.all([
                ...socialUsers.map(async (socialUser) => {
                    const referralSocialUser =
                        await this.getLatestReferralByChannelUserId({
                            companyId,
                            channelUserId: socialUser.channelUserId,
                        });
                    return referralSocialUser;
                }),
            ]);
            const data: ReferralInfo[] = [];
            for (const referral of referralSocialUsers) {
                if (referral) {
                    data.push(referral);
                }
            }
            referralData.push(...data);
        }

        if (appointment?.channelUserId) {
            const channelUserId = appointment.channelUserId;
            const referralChannelUser =
                await this.getLatestReferralByChannelUserId(
                    {
                        companyId,
                        channelUserId,
                    },
                    referralType,
                );
            if (referralChannelUser) {
                referralData.push(referralChannelUser);
            }
        }

        if (!appointment?.channelUserId && appointment?.member) {
            const memberChannelIds =
                await this.memberChannelIdSvc.findByMemberId(
                    appointment.member.id,
                );
            for (const memberChannelId of memberChannelIds) {
                const referralChannelUser =
                    await this.getLatestReferralByChannelUserId(
                        {
                            companyId,
                            channelUserId: memberChannelId.channelUserId,
                        },
                        referralType,
                    );
                if (referralChannelUser) {
                    referralData.push(referralChannelUser);
                }
            }
        }
        if (appointment?.member) {
            const memberReferrals =
                await this.referralMemberSvc.getReferralInfosByMemberId(
                    appointment.member.id,
                    referralType,
                );
            referralData.push(...memberReferrals);
        }
        // 先不加上會員的手機號碼
        const mobiles: string[] = [];
        if (appointment.mobile) {
            mobiles.push(appointment.mobile);
        }
        if (mobiles.length) {
            const mobileReferral = await this.getReferralsByMobiles(
                companyId,
                mobiles,
                referralType,
            );
            referralData.push(...mobileReferral);
        }
        this.referralLibSvc.sortLatestReferralData(referralData);
        return referralData.length ? referralData[0] : null;
    }

    async getReferralsByMobiles(
        companyId: number,
        mobiles: string[],
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ) {
        const { rows } = await this.referralAppointmentRepo.search({
            companyId,
            mobiles,
            referralType,
        });
        const referralData = rows.map((row) => ({
            companyId: row.companyId,
            referralAppointmentId: row.id,
            referralCode: row.referralCode,
            referralDate: row.referralDate,
            referralType: row.referralType,
            appointmentId: row.appointmentId,
            updatedAt: row.updatedAt,
        }));
        this.referralLibSvc.sortLatestReferralData(referralData);
        return referralData;
    }

    /**
     * 從預約ID取得綜合的最新轉介資料
     * - 社群人員的轉介資料
     * - 與預約有關聯的社群人員的轉介資料
     * @param appointmentId
     * @param referralType
     * @returns
     */
    async getLatestReferralByChannelUserId(
        params: FindReferralByChannelUserIdParams,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ) {
        const referralData: ReferralInfo[] = [];
        const [referralAppointment, referralSocialUser] = await Promise.all([
            this.referralAppointmentRepo.findLatestByChannelUserIdParams(
                params,
                referralType,
            ),
            this.referralSocialUserRepo.findLatestByChannelUserId(
                params,
                referralType,
            ),
        ]);
        if (referralAppointment) {
            referralData.push({
                companyId: referralAppointment.companyId,
                referralAppointmentId: referralAppointment.id,
                referralCode: referralAppointment.referralCode,
                referralDate: referralAppointment.referralDate,
                referralType: referralAppointment.referralType,
                appointmentId: referralAppointment.appointmentId,
                updatedAt: referralAppointment.updatedAt,
            });
        }
        if (referralSocialUser) {
            referralData.push({
                companyId: referralSocialUser.companyId,
                referralSocialUserId: referralSocialUser.id,
                referralCode: referralSocialUser.referralCode,
                referralDate: referralSocialUser.referralDate,
                referralType: referralSocialUser.referralType,
                channelUserId: referralSocialUser.channelUserId,
                socialUserId: referralSocialUser.socialUserId,
                updatedAt: referralSocialUser.updatedAt,
            });
        }
        this.referralLibSvc.sortLatestReferralData(referralData);
        return referralData.length ? referralData[0] : null;
    }

    async getLatestReferralBySocialUserId(
        socialUserId: number,
        referralType = EnumReferralType.DOCTOR_REFERRAL,
    ): Promise<ReferralInfo | null> {
        const socialUser = await this.socialUserRepo.findOneOrError(
            socialUserId,
        );
        return this.getLatestReferralByChannelUserId(
            {
                companyId: socialUser.companyId,
                channelUserId: socialUser.channelUserId,
            },
            referralType,
        );
    }
}
