import { Utils } from '@clinico/clinico-node-framework';
import db from '@clinico/clinico-persistence';
import { EnumRegion } from '@clinico/clinico-persistence/dist/base.enum';
import * as httpErrors from 'http-errors';
import { Transaction } from 'sequelize';
import { Inject, Service } from 'typedi';
import logger from '@/modules/logger/logger.service';
import { CompanyService } from '../company/company.service';
import { MemberService } from '../member/member.service';
import { EnumCompanyCode } from '../memberAccount/memberAccount.interface';
import { MemberSNService } from '../memberSN/memberSN.service';
import { EnumQueueType } from '../mq/mq.interface';
import { MqService } from '../mq/mq.service';
import { UserService } from '../user/user.service';
import { CochlearImplantSocialUsersRepository } from './cochlearImplantSocialUsers.repo';
import {
    ReqCochlearImplantMemberDTO,
    ReqCochlearImplantSocialUserBindBodyDTO,
} from './types/cochlearImplant.rest.type';
import { ICreateCochlearImplantSocialUserParams } from './types/cochlearImplant.type';
import { VStoreCISurgeriesRepository } from './v_storeCISurgeries.repo';

@Service()
export class CochlearImplantService {
    @Inject()
    private memberService: MemberService;
    @Inject()
    private mqService: MqService;
    @Inject()
    private companyService: CompanyService;
    @Inject()
    private userService: UserService;
    @Inject()
    private ciSocialUserRepo: CochlearImplantSocialUsersRepository;
    @Inject()
    private vStoreCISurgeriesRepo: VStoreCISurgeriesRepository;
    @Inject()
    private memberSNService: MemberSNService;
    /**
     * 電子耳社群用戶綁定門市會員流程
     * @param body
     * @returns
     */
    async socialUserBindTask(body: ReqCochlearImplantSocialUserBindBodyDTO) {
        const { name, birthday, channelType, channelUserId, channelUserName } =
            body;
        // 1. 檢查是否有相同姓名生日的門市會員
        const company = await this.companyService.findOneByCode(
            EnumCompanyCode.CLINICO,
        );
        const { rows } = await this.memberService.search({
            name,
            birthday,
            companyId: company.id,
        });
        let matchMember = rows[0];

        const tx = await db.sequelize.transaction();

        try {
            // 2.若沒有對應會員，則幫他創建
            if (!matchMember) {
                matchMember = await this.createCIMember(
                    tx,
                    company.id,
                    name,
                    birthday,
                );
            }

            // 3. 綁定會員
            await this.socialUserBindMember(tx, {
                channelType,
                channelUserId,
                channelUserName,
                memberId: matchMember.id,
            });

            await tx.commit();
            logger.info(
                `[電子耳社群會員綁定] 會員編號(${matchMember.code}) 綁定成功`,
            );
            // mq sync
            // TODO 如果通知mq失敗？
            await this.mqService.send({
                regionId: matchMember.regionId,
                companyId: matchMember.companyId,
                queue: EnumQueueType.NewMember,
                key: matchMember.id,
            });
            return;
        } catch (err) {
            await tx.rollback();
            logger.info(`[電子耳社群會員綁定] 操作失敗：${err}`);
            throw err;
        }
    }

    /**
     * 創建電子耳會員
     * @param tx
     * @param name 姓名
     * @param birthday 生日
     * @returns
     */
    async createCIMember(
        tx: Transaction,
        companyId: number,
        name: string,
        birthday: string,
    ) {
        // 1. 取得預設創建人
        const createUser = await this.userService.findOneOrErrorByCode('IT001');
        // 2. 產生電子耳自訂客編
        const nextCustomCode = await this.getNextCICustomCode(tx);
        // 3. 創建會員
        const newMember = await this.memberService.createCIMember(tx, {
            regionId: EnumRegion.TW,
            createdUserId: createUser.id,
            companyId,
            name,
            birthday,
            customCode: nextCustomCode,
        });
        return newMember;
    }

    /**
     * 取得新電子耳自訂客編
     * @param tx
     * @returns
     */
    async getNextCICustomCode(tx: Transaction) {
        // 鎖定當前自訂客編最大值的會員
        const maxCode =
            (await this.memberService.lockMaxCICustomCodeMember(tx)) ??
            'P00000';
        // 取得數字部分
        const maxNum = Number(maxCode.substring(1));
        if (isNaN(maxNum))
            throw new httpErrors.BadRequest('fail to gen new custom code');
        let newNum = String(maxNum + 1);
        // 小於五位數時在前方補0
        while (newNum.length < 5) {
            newNum = '0' + newNum;
        }
        const nextCode = 'P' + String(newNum);
        return nextCode;
    }

    /**
     * 電子耳社群用戶綁定門市會員
     * @param tx
     * @param params
     * @returns
     */
    async socialUserBindMember(
        tx: Transaction,
        params: ICreateCochlearImplantSocialUserParams,
    ) {
        this.ciSocialUserRepo.transaction = tx;
        // 1. 找到memberId是否已有綁定紀錄
        let result = await this.ciSocialUserRepo.search({
            memberId: params.memberId,
        });
        const matchMemberIdSocialUser = result.rows?.[0];

        // 2. 如果memberId已存在但channelUserId不同，要噴錯
        // ex: 同個會員用不同line來綁定
        if (
            matchMemberIdSocialUser &&
            matchMemberIdSocialUser?.channelUserId != params.channelUserId
        )
            throw new httpErrors.BadRequest('memberId already bind');

        // 3. 找到channelUserId是否已有綁定紀錄
        result = await this.ciSocialUserRepo.search({
            channelUserId: params.channelUserId,
            channelType: params.channelType,
        });
        const matchChannelUserIdSocialUser = result.rows?.[0];

        // 4. 如果channelUserId已存在但memberId不同，要噴錯
        // ex: 同個line要綁定在不同會員
        if (
            matchChannelUserIdSocialUser &&
            matchChannelUserIdSocialUser?.memberId != params.memberId
        )
            throw new httpErrors.BadRequest('channelUserId already bind');

        // 5. 如果memberId不存在則創建綁定紀錄
        if (!matchMemberIdSocialUser)
            return this.ciSocialUserRepo.create(params);

        return matchMemberIdSocialUser;
    }

    async getMemberInfo(params: ReqCochlearImplantMemberDTO) {
        const { channelType, channelUserId } = params;
        // 先找出對應社群用戶
        const { rows } = await this.ciSocialUserRepo.search({
            channelType,
            channelUserId,
        });

        if (!rows || !rows[0]) return {};

        const socialUser = rows[0];
        const member = socialUser.member;

        if (!member) return {};

        // 僅搜尋電子耳相關手術及產品
        const ciMaterialTypes = ['E', 'F', 'G'];

        // 搜尋手術資料
        const { rows: surgeries } = await this.vStoreCISurgeriesRepo.search({
            memberCode: member.code,
            materialTypes: ciMaterialTypes,
        });

        // 搜尋產品保固
        const { rows: snList } = await this.memberSNService.search({
            memberId: member.id,
            materialTypeCodes: ciMaterialTypes,
        });

        const response = {
            channelUserId,
            member: {
                code: member.code,
                name: member.name,
                birthday: member.birthday,
                phone: member.phone,
                cellPhone: member.cellPhone,
            },
            surgeries,
            warranty: snList.map((sn) => ({
                sn: sn.SN,
                warrantyStartDate: sn.warrantyStartDate,
                warrantyEndDate: sn.warrantyEndDate,
            })),
        };

        return response;
    }

    async getS3FileUrlByFolder(folder: string) {
        const bucket = 'clinico-cochlear';
        const fileUrls: Record<string, string>[] = [];
        const s3ObjectList = await Utils.AWS.S3.listObjects({
            bucket,
            folder,
        });

        s3ObjectList?.Contents?.map((c) => {
            if (!c.Key) return;
            const [, fileName] = c.Key.split('/');
            if (!fileName) return;
            fileUrls.push({
                fileName,
                url: Utils.AWS.S3.getSignedUrl({
                    bucket,
                    key: c.Key,
                }),
            });
        });
        return fileUrls;
    }

    async getSocialUserFaq() {
        const [N8, N7, Kanso2, Baha6Max] = await Promise.all([
            this.getS3FileUrlByFolder('n8/'),
            this.getS3FileUrlByFolder('n7/'),
            this.getS3FileUrlByFolder('kanso_2/'),
            this.getS3FileUrlByFolder('baha_6_max/'),
        ]);

        return {
            N8,
            N7,
            Kanso2,
            Baha6Max,
        };
    }
}
