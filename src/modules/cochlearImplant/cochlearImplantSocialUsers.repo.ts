import {
    CochlearImplantSocialUsers,
    Member,
} from '@clinico/clinico-persistence';
import * as httpErrors from 'http-errors';
import * as _ from 'lodash';
import { WhereOptions } from 'sequelize';
import { Service } from 'typedi';
import { ISearchResult } from '@/common/interfaces/common.interface';
import { BaseRepository } from '@/common/providers/base.repo';
import {
    ICreateCochlearImplantSocialUserParams,
    ISearchCochlearImplantSocialUsersParams,
} from './types/cochlearImplant.type';

@Service()
export class CochlearImplantSocialUsersRepository extends BaseRepository<CochlearImplantSocialUsers> {
    async search(
        params: ISearchCochlearImplantSocialUsersParams,
    ): Promise<ISearchResult<CochlearImplantSocialUsers[]>> {
        const where: WhereOptions = {
            deleted: false,
        };

        if (params.id) where.id = params.id;

        if (params.channelType) where.channelType = params.channelType;

        if (params.channelUserId) where.channelUserId = params.channelUserId;

        if (params.memberId) where.memberId = params.memberId;

        return CochlearImplantSocialUsers.findAndCountAll({
            where,
            include: [Member],
            transaction: this.transaction,
            offset: params.offset,
            limit: params.limit,
            order: [['id', 'DESC']],
        });
    }

    async create(params: ICreateCochlearImplantSocialUserParams) {
        const data = CochlearImplantSocialUsers.build({
            ...params,
        });

        return data.save({ transaction: this.transaction });
    }

    async searchOneOrError(params: ISearchCochlearImplantSocialUsersParams) {
        const { rows } = await this.search(params);

        if (!rows || rows.length == 0 || _.isEmpty(rows[0])) {
            throw new httpErrors.NotFound(
                'Cochlear Implant social user not found',
            );
        }

        if (rows.length > 1) {
            throw new httpErrors.NotFound(
                'Cochlear Implant social user redundancy',
            );
        }

        return rows[0];
    }
}
