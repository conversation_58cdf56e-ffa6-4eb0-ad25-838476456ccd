import { Helpers } from '@clinico/clinico-node-framework';
import { Service } from 'typedi';
import TipTopDb from '@/common/databases/tiptop.database';
import { ISearchResult } from '@/common/interfaces/common.interface';
import {
    SearchVStoreCISurgeriesParams,
    VStoreCISurgeries,
} from './types/cochlearImplant.type';

@Service()
export class VStoreCISurgeriesRepository {
    async search(
        params: SearchVStoreCISurgeriesParams,
    ): Promise<ISearchResult<VStoreCISurgeries[]>> {
        const sql = TipTopDb('V_STORE_CI_SURGERIES').withSchema('DBUSER1');

        if (params.memberCode) {
            sql.andWhere({ MEMBER_CODE: params.memberCode });
        }

        if (params.materialTypes) {
            sql.whereIn('MATERIAL_TYPE', params.materialTypes);
        }

        const countSql = sql.clone();
        const countRaw = countSql.count('* as count').toQuery();
        const countResult = await TipTopDb.raw(countRaw);
        const count = Helpers.Str.toIntOrZero(countResult[0].count);

        sql.orderBy('SURGERY_DATE', 'asc');

        if (params.limit) {
            sql.limit(params.limit);
        }
        if (params.offset) {
            sql.offset(params.offset);
        }

        const raw = sql
            .select(
                'MEMBER_CODE as memberCode',
                'MEMBER_NAME as memberName',
                'SURGERY_SEQ as surgerySeq',
                'MATERIAL_CODE as materialCode',
                'SN as sn',
                'SPEC as spec',
                'MATERIAL_TYPE as materialType',
                'GEN as gen',
                'ELECTRODE as electrode',
                'EARS as ears',
                'SALE_DATE as saleDate',
                'SURGERY_HOSPITAL_CODE as surgeryHospitalCode',
                'HOSPITAL_NAME as hospitalName',
                'DOCTOR_NAME as doctorName',
                'SURGERY_DATE as surgeryDate',
                'ACTIVE_DATE as activeDate',
                'SURGERY_REMARK as surgeryRemark',
                'IDENTITY as identity',
                'SURGERY_ORDER as surgeryOrder',
            )
            .toQuery();
        const rows = await TipTopDb.raw<VStoreCISurgeries[]>(raw);

        return { rows, count };
    }
}
