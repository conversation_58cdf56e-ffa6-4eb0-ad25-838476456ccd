import { Helpers } from '@clinico/clinico-node-framework';
import {
    Body,
    Get,
    HeaderParam,
    JsonController,
    Post,
    QueryParams,
    UseBefore,
} from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import Container, { Service } from 'typedi';
import { RequiredAccessKeyAuthenticated } from '@/common/middlewares/auth/auth.koa.middleware';
import logger from '@/modules/logger/logger.service';
import { CochlearImplantService } from './cochlearImplant.service';
import {
    ReqCochlearImplantMemberDTO,
    ReqCochlearImplantSocialUserBindBodyDTO,
} from './types/cochlearImplant.rest.type';

@Service()
@JsonController('/cochlear-implant')
@UseBefore(RequiredAccessKeyAuthenticated())
export class CochlearImplantController {
    private ciService = Container.get(CochlearImplantService);

    @Post('/social-user/member/bind')
    @OpenAPI({
        summary: '電子耳社群會員綁定',
    })
    async bookingBySocial(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqCochlearImplantSocialUserBindBodyDTO,
    ) {
        try {
            await this.ciService.socialUserBindTask(body);
            return Helpers.Json.success();
        } catch (err) {
            logger.info(`電子耳社群會員綁定失敗：${err}`);
            throw err;
        }
    }

    @Get('/social-user/member')
    @OpenAPI({
        summary: '電子耳社群會員查詢',
    })
    async member(@QueryParams() query: ReqCochlearImplantMemberDTO) {
        try {
            const result = await this.ciService.getMemberInfo(query);
            return Helpers.Json.success(result);
        } catch (err) {
            logger.info(`電子耳社群會員查詢失敗：${err}`);
            throw err;
        }
    }

    @Get('/social-user/faq')
    @OpenAPI({
        summary: '電子耳社群會員faq',
    })
    async faq() {
        try {
            const result = await this.ciService.getSocialUserFaq();
            return Helpers.Json.success(result);
        } catch (err) {
            logger.info(`電子耳社群會員FAQ查詢失敗：${err}`);
            throw err;
        }
    }
}
