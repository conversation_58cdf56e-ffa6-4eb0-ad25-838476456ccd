import { EnumCochlearImplantSocialUsersChannelType } from '@clinico/clinico-persistence/dist/models/cochlearImplantSocialUsers.model';
import { IsEnum, IsString } from 'class-validator';
import { JSONSchema } from 'class-validator-jsonschema';

export class ReqCochlearImplantSocialUserBindBodyDTO {
    @JSONSchema({
        description: '社群類型',
        example: EnumCochlearImplantSocialUsersChannelType.LINE,
    })
    @IsEnum(EnumCochlearImplantSocialUsersChannelType)
    channelType: EnumCochlearImplantSocialUsersChannelType;

    @JSONSchema({
        description: 'line id',
        example: 'lineId',
    })
    @IsString()
    channelUserId: string;

    @JSONSchema({
        description: 'line 暱稱',
        example: 'lineUserName',
        nullable: true,
    })
    @IsString()
    channelUserName?: string;

    @JSONSchema({
        description: '姓名',
    })
    @IsString()
    name: string;

    @JSONSchema({
        description: '生日',
    })
    @IsString()
    birthday: string;
}

export class ReqCochlearImplantMemberDTO {
    @JSONSchema({
        description: '社群類型',
        example: EnumCochlearImplantSocialUsersChannelType.LINE,
    })
    @IsEnum(EnumCochlearImplantSocialUsersChannelType)
    channelType: EnumCochlearImplantSocialUsersChannelType;

    @JSONSchema({
        description: 'line id',
        example: 'lineId',
    })
    @IsString()
    channelUserId: string;
}
