import { EnumCochlearImplantSocialUsersChannelType } from '@clinico/clinico-persistence/dist/models/cochlearImplantSocialUsers.model';
import { IBaseSearchParams } from '@/common/interfaces/common.interface';

export interface ISearchCochlearImplantSocialUsersParams
    extends IBaseSearchParams {
    memberId?: number;
    channelUserId?: string;
    channelType?: EnumCochlearImplantSocialUsersChannelType;
}

export interface ICreateCochlearImplantSocialUserParams {
    channelType: EnumCochlearImplantSocialUsersChannelType;
    channelUserId: string;
    channelUserName?: string;
    memberId: number;
}

export type VStoreCISurgeries = {
    // 會員編號
    memberCode: string;
    // 會員姓名
    memberName: string;
    // 開刀項次
    surgerySeq: number;
    // 料號
    materialCode: string;
    // 序號
    sn: string;
    // 規格
    spec: string;
    // 料件大類
    materialType: string;
    // 世代
    gen: string;
    // 電極
    electrode: string;
    // 左右耳
    ears: number;
    // 銷售日期
    saleDate: Date;
    // 開刀醫院代號
    surgeryHospitalCode: string;
    // 醫院名稱
    hospitalName: string;
    // 開刀醫生
    doctorName: string;
    // 開刀日期
    surgeryDate: Date;
    // 開頻日期
    activeDate: Date;
    // 開刀備註
    surgeryRemark: string;
    // 身份
    identity: string;
    // 開刀順序
    surgeryOrder: string;
};

export interface SearchVStoreCISurgeriesParams extends IBaseSearchParams {
    memberCode?: string;
    materialTypes?: string[];
}
