import {
    AppointmentLogistics,
    AppointmentLogisticsRecipientInfo,
    WorkDiary,
} from '@clinico/clinico-persistence';
import Appointment, {
    EnumAppointmentDataSource,
} from '@clinico/clinico-persistence/dist/models/appointment.model';
import AppointmentContactHistory from '@clinico/clinico-persistence/dist/models/appointmentContactHistory.model';
import AppointmentReferral from '@clinico/clinico-persistence/dist/models/appointmentReferral.model';
import Booking from '@clinico/clinico-persistence/dist/models/booking.model';
import Business from '@clinico/clinico-persistence/dist/models/business.model';
import * as Koa from 'koa';
import * as moment from 'moment';
import {
    Resolver,
    Query,
    Mutation,
    Arg,
    Ctx,
    UseMiddleware,
    Args,
    Root,
    FieldResolver,
    ResolverFilterData,
    Subscription,
} from 'type-graphql';
import { Service, Inject } from 'typedi';
import { RequiredUserAuthenticated } from '@/common/middlewares/auth/auth.graphql.middleware';
import { AppointmentContactHistoryService } from '../appointmentContactHistory/appointmentContactHistory.service';
import {
    AppointmentContactHistoriesWrap,
    SearchAppointmentContactHistoryArgs,
} from '../appointmentContactHistory/appointmentContactHistory.type';
import { AppointmentLogisticsRepository } from '../appointmentLogistics/repositories/appointmentLogistics.repo';
import { AppointmentLogisticsRecipientInfoRepository } from '../appointmentLogisticsRecipientInfo/repositories/appointmentLogisticsRecipientInfo.repo';
import { AppointmentReferralRepository } from '../appointmentReferral/repositories/appointmentReferral.repo';
import { BookingService } from '../booking/booking.service';
import { BusinessService } from '../business/business/providers/business.service';
import { MenuBadgeService } from '../menuBadge/menuBadge.service';
import { ReferralService } from '../referral/services/referral.service';
import { DoctorReferralInfo } from '../referral/types/referral.gql.type';
import { EnumSMSLogType } from '../smsLog/smsLog.interface';
import { SMSLogService } from '../smsLog/smsLog.service';
import { SMSLog } from '../smsLog/smsLog.type';
import { IUserPayload } from '../user/user.type';
import { WorkDiaryService } from '../workDiary/workDiary.service';
import {
    SearchAppointmentArgs,
    CreateAppointmentInput,
    UpdateAppointmentInput,
    AppointmentsWrap,
    AppointmentItemAndSubitem,
    AppointmentSubscription,
    AppointmentSubscriptionArgs,
    AppointmentSocialUser,
} from './appointment.type';
import { AppointmentService } from './services/appointment.service';
import { AppointmentServiceItemService } from './services/appointmentServiceItem.service';
import { AppointmentSocialUserService } from './services/appointmentSocialUser.service';

@Service()
@Resolver((of) => Appointment)
class AppointmentResolver {
    @Inject()
    private appointmentService: AppointmentService;
    @Inject()
    private appointmentServiceItemService: AppointmentServiceItemService;
    @Inject()
    private appointmentContactHistoryService: AppointmentContactHistoryService;
    @Inject()
    private menuBadgeService: MenuBadgeService;
    @Inject()
    private bookingService: BookingService;
    @Inject()
    private smsLogService: SMSLogService;
    @Inject()
    private businessService: BusinessService;
    @Inject()
    private workDiaryService: WorkDiaryService;
    @Inject()
    private referralSvc: ReferralService;
    @Inject()
    private appointmentReferralRepo: AppointmentReferralRepository;
    @Inject()
    private appointmentLogisticsRecipientInfoRepo: AppointmentLogisticsRecipientInfoRepository;
    @Inject()
    private appointmentLogisticsRepo: AppointmentLogisticsRepository;
    @Inject()
    private appointmentSocialUserService: AppointmentSocialUserService;

    @UseMiddleware(RequiredUserAuthenticated)
    @Query((returns) => [Appointment])
    async appointments(
        @Args() params: SearchAppointmentArgs,
        @Ctx() ctx: Koa.Context,
    ): Promise<Appointment[]> {
        const regionId = ctx.req['regionId'];
        const data = await this.appointmentService.search({
            ...params,
            regionId,
        });
        return data.rows;
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Query((returns) => AppointmentsWrap)
    async appointmentsWrap(
        @Args() params: SearchAppointmentArgs,
        @Ctx() ctx: Koa.Context,
    ): Promise<AppointmentsWrap> {
        const regionId = ctx.req['regionId'];
        const data = await this.appointmentService.search({
            ...params,
            regionId,
        });
        return { appointments: data.rows, count: data.count };
    }

    @FieldResolver((type) => [AppointmentItemAndSubitem])
    async appointmentItemAndSubitems(
        @Root() appointment: Appointment,
    ): Promise<AppointmentItemAndSubitem[]> {
        const itemAndSubitem: AppointmentItemAndSubitem[] = [];
        const rows =
            await this.appointmentServiceItemService.searchByAppointmentIdUseDataloader(
                appointment.id,
            );

        for (const item of rows ?? []) {
            itemAndSubitem.push({
                appointmentItem: item.appointmentItem,
                appointmentSubItems: item.appointmentServiceSubitem.map(
                    (data) => data.appointmentSubitem,
                ),
            });
        }

        return itemAndSubitem;
    }

    @FieldResolver((type) => [AppointmentContactHistory], {
        nullable: 'itemsAndList',
    })
    async contactHistories(
        @Root() appointment: Appointment,
        @Args() args: SearchAppointmentContactHistoryArgs,
    ): Promise<AppointmentContactHistory[]> {
        // If no additional filters are provided, use dataloader for better performance
        if (!args.limit && !args.offset) {
            return await this.appointmentContactHistoryService.searchByAppointmentIdUseDataloader(
                appointment.id,
            );
        }
        // Fallback to original method when pagination is needed
        const data = await this.appointmentContactHistoryService.search({
            ...args,
            appointmentId: appointment.id,
        });
        return data.rows;
    }

    @FieldResolver((type) => AppointmentContactHistoriesWrap)
    async contactHistoriesWrap(
        @Root() appointment: Appointment,
        @Args() args: SearchAppointmentContactHistoryArgs,
    ): Promise<AppointmentContactHistoriesWrap> {
        // contactHistoriesWrap needs count, so we keep using the original search method
        const data = await this.appointmentContactHistoryService.search({
            ...args,
            appointmentId: appointment.id,
        });
        return { contactHistories: data.rows, count: data.count };
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => Appointment, { name: 'createAppointment' })
    async create(
        @Arg('input') input: CreateAppointmentInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Appointment> {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];
        const appointment =
            await this.appointmentService.createAppointmentAndItems({
                ...input,
                createdUserId: payload.id,
                regionId,
            });
        return appointment;
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => Appointment, { name: 'updateFieldsAppointment' })
    async update(
        @Arg('input') input: UpdateAppointmentInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Appointment> {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];
        const res = await this.appointmentService.updateAppointmentAndItems({
            ...input,
            updatedUserId: payload.id,
            regionId,
        });
        /** Menu badge update */
        await this.menuBadgeService.publish({
            userId: payload.id,
            storeId: res.appointment.storeId,
            keys: res.keys,
        });
        return res.appointment;
    }

    @FieldResolver((type) => EnumAppointmentDataSource, { nullable: true })
    async appointmentDataSource(
        @Root() appointment: Appointment,
    ): Promise<EnumAppointmentDataSource | null> {
        if (appointment.appointmentDataSource) {
            return EnumAppointmentDataSource[
                appointment.appointmentDataSource.code
            ];
        }
        return null;
    }

    @FieldResolver((type) => Booking, { nullable: true })
    async rentAppointmentBooking(
        @Root() appointment: Appointment,
    ): Promise<Booking | null> {
        if (!appointment.returnAppointmentBooking) {
            return await this.bookingService.findOneByRentAppointmentIdUseDataloader(
                appointment.id,
            );
        }
        return null;
    }

    @FieldResolver((type) => Booking, { nullable: true })
    async returnAppointmentBooking(
        @Root() appointment: Appointment,
    ): Promise<Booking | null> {
        if (!appointment.rentAppointmentBooking) {
            return await this.bookingService.findOneByReturnAppointmentIdUseDataloader(
                appointment.id,
            );
        }
        return null;
    }

    @FieldResolver((type) => [SMSLog])
    async SMSLogs(@Root() appointment: Appointment): Promise<SMSLog[]> {
        const result = await this.smsLogService.search({
            type: EnumSMSLogType.appointment,
            keyId: appointment.id,
        });
        return result.rows;
    }

    @Subscription((returns) => AppointmentSubscription, {
        topics: 'APPOINTMENTS',
        filter: ({
            payload,
            args,
        }: ResolverFilterData<
            AppointmentSubscription,
            AppointmentSubscriptionArgs,
            any
        >) => {
            const appointment = payload.appointment;
            const results: boolean[] = [];
            if (args.storeId) {
                results.push(appointment.store?.id == args.storeId);
            }
            if (args.date1 && args.date2) {
                results.push(
                    moment(appointment.date).isBetween(
                        moment(args.date1),
                        moment(args.date2),
                        'day',
                        '[]',
                    ),
                );
            }
            if (args.audiologistId) {
                results.push(appointment.audiologist?.id == args.audiologistId);
            }
            return results.reduce((acc, cur) => acc === cur, true);
        },
    })
    async appointmentSubscription(
        @Root() payload: AppointmentSubscription,
        @Args() args: AppointmentSubscriptionArgs,
    ): Promise<AppointmentSubscription> {
        return payload;
    }

    @FieldResolver((returns) => Business, { nullable: true })
    async business(@Root() appointment: Appointment): Promise<Business | null> {
        if (appointment.businessId) {
            return await this.businessService.findOne(appointment.businessId);
        }

        return null;
    }

    @FieldResolver((returns) => DoctorReferralInfo, {
        nullable: true,
        description: '取得該筆預約的醫生轉介資訊',
    })
    async doctorReferralInfo(
        @Root() appointment: Appointment,
    ): Promise<DoctorReferralInfo | null> {
        return this.referralSvc.getReferralAppointmentId(appointment.id);
    }

    @FieldResolver((returns) => WorkDiary, { nullable: true })
    async workDiary(
        @Root() appointment: Appointment,
    ): Promise<WorkDiary | null> {
        return await this.workDiaryService.findOneByAppointmentId(
            appointment.id,
        );
    }

    @FieldResolver((returns) => AppointmentReferral, {
        nullable: true,
        description: '取得該筆預約的後續導流',
    })
    async appointmentReferral(
        @Root() appointment: Appointment,
    ): Promise<AppointmentReferral | null> {
        return this.appointmentReferralRepo.findOneByAppointmentId(
            appointment.id,
        );
    }

    @FieldResolver((returns) => AppointmentLogisticsRecipientInfo, {
        nullable: true,
        description: '取得該筆預約的物流資訊',
    })
    async logisticsRecipientInfo(
        @Root() appointment: Appointment,
    ): Promise<AppointmentLogisticsRecipientInfo | null> {
        return this.appointmentLogisticsRecipientInfoRepo.findOneByAppointmentId(
            appointment.id,
        );
    }

    @FieldResolver((returns) => [AppointmentLogistics], {
        nullable: true,
        description: '取得該筆預約的物流資訊',
    })
    async logistics(
        @Root() appointment: Appointment,
    ): Promise<AppointmentLogistics[] | null> {
        const data = await this.appointmentLogisticsRepo.findByAppointmentId(
            appointment.id,
        );
        return data;
    }

    @FieldResolver((returns) => AppointmentSocialUser, {
        description: '預約單關聯社群用戶',
        nullable: true,
    })
    async socialUser(@Root() appointment: Appointment) {
        // 預約單沒有綁來源或社群用戶id，直接略過查詢
        if (
            !appointment.channelUserId ||
            // 暫時只開放一般社群預約做此查詢，未來如果要開放iCare等社群查詢，可以再調整這邊的邏輯
            appointment.dataSource != EnumAppointmentDataSource.Social
        ) {
            return null;
        }
        // 根據預約來源搜尋社群用戶資訊
        return this.appointmentSocialUserService.getAppointmentSocialUser(
            appointment.dataSource,
            appointment.channelUserId,
            appointment.store?.companyId,
        );
    }
}
