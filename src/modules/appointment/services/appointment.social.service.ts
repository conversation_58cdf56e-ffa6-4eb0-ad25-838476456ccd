import { Helpers, Utils } from '@clinico/clinico-node-framework';
import Appointment, {
    EnumAppointmentDataSource,
    EnumAppointmentIdentity,
    EnumAppointmentTimeType,
} from '@clinico/clinico-persistence/dist/models/appointment.model';
import * as httpErrors from 'http-errors';
import * as moment from 'moment';
import { Inject, Service } from 'typedi';
import { EVENTS } from '@/consts';
import { ClinicoSoundMemberAccountRepository } from '@/modules/clinicoSound/memberAccount/repositories/memberAccount.repo';
import logger from '@/modules/logger/logger.service';
import { EnumCompanyCode } from '@/modules/memberAccount/memberAccount.interface';
import { PotentialTargetEvent } from '@/modules/potentialTarget/events/potentialTarget.event';
import { SMSService } from '@/modules/sms/sms.service';
import { EnumSMSLogType } from '@/modules/smsLog/smsLog.interface';
import { SocialUserRepository } from '@/modules/socialUser/repositories/socialUser.repo';
import { StoreGatewayUseCase } from '@/modules/store/store.gateway.usecase';
import { StoreService } from '@/modules/store/store.service';
import { ReqSocialBookingCochlearImplantBodyDTO } from '../appointment.rest.type';
import {
    CreateAppointmentByShoppingCenterParams,
    CreateAppointmentBySocialParams,
    CreateAppointmentForClinicoSoundParams,
    CreateAppointmentInternalInput,
    EnumAppointmentServiceItem,
    EnumAppointmentServiceSubItem,
} from '../appointment.type';
import { AppointmentCochlearImplantService } from './appointment.ci.service';
import { AppointmentService } from './appointment.service';
import { AppointmentServiceItemService } from './appointmentServiceItem.service';

@Service()
export class AppointmentSocialService {
    @Inject()
    private appointmentService: AppointmentService;
    @Inject()
    private socialUserRepo: SocialUserRepository;
    @Inject()
    private storeSvc: StoreService;
    @Inject()
    private potentialTargetEvent: PotentialTargetEvent;
    @Inject()
    private smsService: SMSService;
    @Inject()
    private clinicoSoundMemberAccountRepo: ClinicoSoundMemberAccountRepository;
    @Inject()
    private storeGatewayUseCase: StoreGatewayUseCase;
    @Inject()
    private appointmentCIService: AppointmentCochlearImplantService;
    @Inject()
    private appointmentServiceItemService: AppointmentServiceItemService;

    /**
     * line@ 粉絲團進行預約
     * @param body
     * @returns
     */
    async createAppointmentBySocial(body: CreateAppointmentBySocialParams) {
        const {
            storeCode,
            timeType,
            time,
            appointmentDate,
            districtId,
            appointmentItemAndSubitems,
            channelUserId,
            channelType,
        } = body;

        const store = await this.storeSvc.findOneOrErrorByCode(storeCode);
        // 檢查預約時間是否合法
        const daysUntilBookingDate =
            await this.storeGatewayUseCase.getDaysUntilBookingDateByStore({
                code: storeCode,
                storeId: store.id,
                daysUntilBooking: store.daysUntilBooking,
            });
        if (!moment(daysUntilBookingDate).isValid()) {
            const error = new httpErrors.BadRequest(
                `get store available booking date fail (${daysUntilBookingDate})`,
            );
            error.code = 'STORE_BOOKING_DATE_INVALID';
            throw error;
        }
        if (moment(appointmentDate).isBefore(moment(daysUntilBookingDate))) {
            const error = new httpErrors.BadRequest(
                `appointment date(${appointmentDate}) is before minimum available booking date(${daysUntilBookingDate})`,
            );
            error.code = 'STORE_BOOKING_DATE_OUT_OF_RANGE';
            throw error;
        }

        const socialUser = await this.socialUserRepo.findOneOrCreateUpdate({
            companyId: store.companyId,
            channelType,
            channelUserId,
        });
        const { mobile, name, email, remark } = body;
        const dataSource = EnumAppointmentDataSource.Social;
        const params: CreateAppointmentInternalInput = {
            storeCode,
            dataSource,
            identity: EnumAppointmentIdentity.Self,
            timeType,
            time,
            appointmentDate,
            mobile,
            name,
            email,
            appointmentItemAndSubitemInput: appointmentItemAndSubitems,
            remark,
            districtId,
            channelUserId,
        };
        const appointment = await this.appointmentService.createInternal(
            params,
        );
        this.potentialTargetEvent.emitter.emit(
            EVENTS.POTENTIAL_TARGET.SOCIAL_USER.FIND_OR_NEW,
            {
                socialUserId: socialUser.id,
                companyId: socialUser.companyId,
            },
        );

        // LINE@預約成功簡訊
        await this.sendSMS(appointment);
        await this.sendSuccessEmail(appointment);

        return appointment;
    }

    /**
     * line@ 粉絲團進行預約
     * @param body
     * @returns
     */
    async createAppointmentBySocialForClinicoSound(
        body: Omit<
            CreateAppointmentForClinicoSoundParams,
            'appointmentItemAndSubitems'
        >,
    ) {
        const {
            storeCode,
            timeType,
            time,
            appointmentDate,
            districtId,
            channelUserId,
        } = body;
        const store = await this.storeSvc.findOneOrErrorByCode(storeCode);
        let memberAccount =
            await this.clinicoSoundMemberAccountRepo.findOneByLineId(
                channelUserId,
            );
        if (store.companyId !== memberAccount?.member?.companyId) {
            memberAccount = null;
        }
        const appointmentItemAndSubitems = []; //預設ClinicoSound維修
        const { mobile, name, email, remark, SN } = body;
        const params: CreateAppointmentInternalInput = {
            storeCode,
            dataSource: EnumAppointmentDataSource.ClinicoSound,
            identity: EnumAppointmentIdentity.Self,
            timeType,
            time,
            appointmentDate,
            mobile,
            name,
            email,
            appointmentItemAndSubitemInput: appointmentItemAndSubitems,
            remark: remark ? `${remark} SN: ${SN}` : `SN: ${SN}`,
            districtId,
            channelUserId,
            memberId: memberAccount?.memberId,
        };
        const appointment = await this.appointmentService.createInternal(
            params,
        );

        return appointment;
    }

    async createAppointmentForShoppingCenter(
        body: CreateAppointmentByShoppingCenterParams,
    ) {
        const { storeCode, timeType, time, appointmentDate, districtId } = body;
        // 先檢查appointment是否有存在相同時間的預約

        const store = await this.storeSvc.findOneOrErrorByCode(storeCode);
        await this.appointmentService.findShoppingCenterAppointmentIsExists({
            storeId: store.id,
            appointmentDate,
            time,
            timeType,
        });
        const appointmentItemAndSubitems = body.appointmentItemAndSubitems; //ShoppingCenter
        const { mobile, name, email, remark } = body;
        const params: CreateAppointmentInternalInput = {
            storeCode,
            dataSource: EnumAppointmentDataSource.Counter,
            identity: EnumAppointmentIdentity.Self,
            timeType,
            time,
            appointmentDate,
            mobile,
            name,
            email,
            appointmentItemAndSubitemInput: appointmentItemAndSubitems,
            remark,
            districtId,
        };
        const appointment = await this.appointmentService.createInternal(
            params,
        );
        return appointment;
    }

    /**
     * LINE@ 預約成功通知門市
     * @param appointment
     */
    private async sendSuccessEmail(appointment: Appointment) {
        const store = appointment.store;
        const company = appointment.store?.company;
        let companyName = '';
        switch (company?.code) {
            case EnumCompanyCode.CLINICO:
                companyName = '科林';
                break;
            case EnumCompanyCode.IB:
                companyName = '聽寶';
                break;
            case EnumCompanyCode.SKD:
                companyName = '濰樂';
                break;
        }
        const storeEmail = store?.email ?? '';
        const subject = `LINE@預約通知信-${companyName}-${store?.name ?? ''}`;
        const serviceItems = appointment.appointmentServiceItems
            .map((i) => i.appointmentItem.name)
            .join(',');
        let body = '';
        const endTime = appointment.endTime ? `- ${appointment.endTime}` : '';
        let appointTimeType = '';
        switch (appointment.timeType) {
            case EnumAppointmentTimeType.Morning:
                appointTimeType = `上午 `;
                break;
            case EnumAppointmentTimeType.Afternoon:
                appointTimeType = `下午 `;
                break;
            case EnumAppointmentTimeType.Night:
                appointTimeType = `晚上 `;
                break;
        }
        body += `門市: ${store?.name ?? ''} <br>`;
        body += `1. 服務項目: ${serviceItems} <br>`;
        body += `2. 來店日期: ${appointment.date} <br>`;
        body += `3. 來店時間: ${appointTimeType}${appointment.time}${endTime} <br>`;
        body += `4. 聯絡資訊 <br>`;
        body += `&nbsp;&nbsp;姓名:${appointment.name} <br>`;
        body += `&nbsp;&nbsp;手機:${appointment.mobile} <br>`;
        body += `<br>`;
        body += `<font color="#FF0000">請務必於當日下班前至未確認預約完成聯繫後，並確認預約時間。</font>`;
        if (Helpers.Env.isProduction() && Boolean(storeEmail)) {
            Utils.Mailer.send({
                to: [storeEmail].toString(),
                subject,
                body,
            });
        } else {
            body += '<hr>';
            body += '【測試資訊】';
            body += '門市信箱: ' + storeEmail + '<br>';
            Utils.Mailer.send({
                to: [
                    '<EMAIL>',
                    '<EMAIL>',
                ].toString(),
                subject: `【測試】${subject}`,
                body,
            });
        }
    }

    /** LINE@預約成功簡訊 */
    private async sendSMS(appointment: Appointment) {
        try {
            const content =
                `【${appointment.store?.company.name ?? 'LINE@預約'}】 ${
                    appointment.name
                }先生/小姐，您好！我們已收到您預約，` +
                `我們將安排專人於3天內與您確認預約日期和時間，預約程序才算完成。若有問題敬請撥打﹕${appointment.store?.phone}，謝謝。`;

            await this.smsService.send({
                subject: 'LINE@預約',
                content,
                mobile: appointment.mobile ?? '',
                type: EnumSMSLogType.appointment,
                keyId: appointment.id,
                companyId: appointment.store?.companyId ?? 1,
            });
        } catch (err: any) {
            logger.error(err);
        }
    }

    /**
     * 電子耳社群預約
     * @param body
     * @returns
     */
    async createAppointmentForCochlearImplant(
        body: ReqSocialBookingCochlearImplantBodyDTO,
    ) {
        const {
            storeCode,
            timeType,
            time,
            appointmentDate,
            districtId,
            appointmentItemAndSubitems,
            mobile,
            name,
            email,
        } = body;
        let { remark } = body;

        // 1. 基本檢查
        const socialUser =
            await this.appointmentCIService.validateForSocialAppointment(body);

        // 處理購買耗材備註
        let needAccessoryWarranty = false;
        let needDamageCertificate = false;
        for (const item of appointmentItemAndSubitems) {
            const hasSupplies = item.appointmentSubItemCodes?.includes(
                EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI]
                    .Supplies,
            );

            if (hasSupplies) {
                needAccessoryWarranty ||= !!item.accessoryWarrantyDoc;
                needDamageCertificate ||= !!item.damageCertificate;
                if (needAccessoryWarranty && needDamageCertificate) break;
            }
        }

        const remarkList: string[] = [];
        if (remark) remarkList.push(remark);
        if (needAccessoryWarranty) remarkList.push('配件保固書');
        if (needDamageCertificate) remarkList.push('損壞證明');

        remark = remarkList.join(',');

        // 2. 建立未確認預約
        const params: CreateAppointmentInternalInput = {
            storeCode,
            dataSource: EnumAppointmentDataSource.CochlearImplant,
            identity: EnumAppointmentIdentity.Self,
            timeType,
            time,
            appointmentDate,
            mobile,
            name,
            appointmentItemAndSubitemInput: appointmentItemAndSubitems,
            districtId,
            // 預約單不放入社群用戶id，以免跟原本的耳科line用戶衝突
            // 耳科若已有channelUserId綁定了不同memberId，會蓋掉下方帶入的memberId
            // channelUserId,
            memberId: socialUser.memberId,
            email,
            remark,
        };
        const appointment = await this.appointmentService.createInternal(
            params,
        );
        const serviceItem =
            await this.appointmentServiceItemService.findOneOrError(
                appointment.appointmentServiceItems[0].id,
            );

        // 3. 發送郵件通知
        await this.appointmentCIService.sendAppointmentCreatedNotify(
            appointment,
            serviceItem,
        );

        return {
            id: appointment.id,
            date: appointment.date,
            time: appointment.time,
            member: {
                code: appointment.member?.code,
                name: appointment.member?.name,
                phone: appointment.member?.phone,
                cellPhone: appointment.member?.cellPhone,
            },
            store: {
                code: appointment.store?.code,
                name: appointment.store?.name,
                phone: appointment.store?.phone,
            },
        };
    }
}
