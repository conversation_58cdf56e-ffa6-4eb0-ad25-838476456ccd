import { Helpers } from '@clinico/clinico-node-framework';
import db, {
    AppointmentItem,
    BookingDevice,
} from '@clinico/clinico-persistence';
import { EnumRegion } from '@clinico/clinico-persistence/dist/base.enum';
import Appointment, {
    EnumAppointmentStatus,
    EnumAppointmentCancelReasonType,
    EnumAppointmentMemberType,
    EnumAppointmentDataSource,
    EnumAppointmentServiceStatus,
} from '@clinico/clinico-persistence/dist/models/appointment.model';
import { EnumAppointmentPaymentStatus } from '@clinico/clinico-persistence/dist/models/appointmentPaymentRecord.model';
import { EnumMemberGender } from '@clinico/clinico-persistence/dist/models/member.model';
import * as DataLoader from 'dataloader';
import * as httpErrors from 'http-errors';
import * as _ from 'lodash';
import { groupBy } from 'lodash';
import * as moment from 'moment';
import { Op } from 'sequelize';
import { Transaction } from 'sequelize/types';
import { Inject, Service } from 'typedi';
import {
    ISearchResult,
    EnumSortType,
} from '@/common/interfaces/common.interface';
import { EVENTS } from '@/consts';
import { AppointmentLogisticsRecipientInfoRepository } from '@/modules/appointmentLogisticsRecipientInfo/repositories/appointmentLogisticsRecipientInfo.repo';
import { AppointmentReferralUseCase } from '@/modules/appointmentReferral/usecases/appointmentReferral.usecase';
import { BookingSMSService } from '@/modules/booking/bookingSMS.service';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import { ICareTestSocialUserReferralsService } from '@/modules/iCareTestSocialUserReferrals/services/iCareTestSocialUserReferrals.service';
import { PotentialTargetEvent } from '@/modules/potentialTarget/events/potentialTarget.event';
import {
    EnumProcessStatus,
    ProcessPotentialTargetByAppointmentParams,
} from '@/modules/potentialTarget/types/potentialTargetAppointment.type';
import { SocialUserMemberService } from '@/modules/socialUser/services/socialUserMember.service';
import { GoogleAdsService } from '../../Google/ads.service';
import {
    IUpdateBookingInput,
    IDeleteBooking,
    ICreateBookingInput,
    ICheckSetDevice,
} from '../../booking/booking.interface';
import { BookingService } from '../../booking/booking.service';
import {
    EnumBookingStatus,
    Booking,
    EnumRentDeviceType,
} from '../../booking/booking.type';
import { CompanyService } from '../../company/company.service';
import { AppointmentItemService } from '../../item/appointmentItem.service';
import { AppointmentSubitemService } from '../../item/appointmentSubitem.service';
import { MemberService } from '../../member/member.service';
import { Member } from '../../member/member.type';
import { MemberChannelIdService } from '../../member/memberChannelId.service';
import { MenuBadge } from '../../menuBadge/menuBadge.type';
import { NotificationService } from '../../notification/notification.service';
import {
    EnumNotificationType,
    EnumNotificationActionComponent,
} from '../../notification/notification.type';
import { pubsub } from '../../pubsub/pubsub.service';
import { StoreService } from '../../store/store.service';
import { UserService } from '../../user/user.service';
import {
    AppointmentItemAndSubitemInput,
    EnumAppointmenSubscriptiontType,
    EnumAppointmentServiceItem,
    EnumAppointmentServiceItemSendEmail,
    FindShoppingCenterAppointmentParams,
    ShoppingCenterAppointmentReqDTO,
} from '../appointment.type';
import {
    ISearchAppointmentParams,
    ICreateAppointmentAndItemsParams,
    IUpdateAppointmentAndItemsParams,
    ICreateAppointmentInternalParams,
    IUpdateAppointmentInternalParams,
    ICancelAppointmentInternalParams,
    IAppointmentPublish,
    IUpdateAppointmentBooking,
    IRefundAppointmentInternalParams,
    IInvoiceAppointmentInternalParams,
} from '../interfaces/appointment.interface';
import {
    AppointmentSort,
    ISearchAppointmentLibParams,
} from '../interfaces/appointment.lib.interface';
import { AppointmentCochlearImplantService } from './appointment.ci.service';
import { AppointmentLibService } from './appointment.lib.service';
import { AppointmentEmailService } from './appointmentEmail.service';
import { AppointmentPaymentRecordService } from './appointmentPaymentRecord.service';
import { AppointmentServiceItemService } from './appointmentServiceItem.service';

@Service()
export class AppointmentService {
    @Inject()
    private appointmentLibService: AppointmentLibService;
    @Inject()
    private memberService: MemberService;
    @Inject()
    private appointmentServiceItemService: AppointmentServiceItemService;
    @Inject()
    private storeService: StoreService;
    @Inject()
    private storeSvc: StoreService;
    @Inject()
    private notificationService: NotificationService;
    @Inject()
    private userService: UserService;
    @Inject()
    private appointmentItemService: AppointmentItemService;
    @Inject()
    private appointmentSubitemService: AppointmentSubitemService;
    @Inject()
    private bookingService: BookingService;
    @Inject()
    private bookingSMSService: BookingSMSService;
    @Inject()
    private googleAdsService: GoogleAdsService;
    @Inject()
    private businessService: BusinessService;
    @Inject()
    private companyService: CompanyService;
    @Inject()
    private socialUserMemberSvc: SocialUserMemberService;
    @Inject()
    private memberChannelIdSvc: MemberChannelIdService;
    @Inject()
    private potentialTargetEvent: PotentialTargetEvent;
    @Inject()
    private appointmentEmailService: AppointmentEmailService;
    @Inject()
    private appointmentPaymentRecordService: AppointmentPaymentRecordService;
    @Inject()
    private appointmentLogisticsRecipientInfoRepo: AppointmentLogisticsRecipientInfoRepository;
    @Inject()
    private appointmentReferralUseCase: AppointmentReferralUseCase;
    @Inject()
    private iCareTestSocialUserReferralService: ICareTestSocialUserReferralsService;
    @Inject()
    private appointmentCIService: AppointmentCochlearImplantService;

    async findOneByBusinessId(
        businessId: number,
        regionId: number,
    ): Promise<Appointment | null> {
        const appointment = await this.search({ businessId, regionId });
        const rows = appointment.rows;

        return rows.length == 1 ? rows[0] : null;
    }

    async searchByMemberIdUseDataloader(
        memberId: number,
    ): Promise<Appointment[]> {
        const res = await this.findByMemberIdDataloader.load(memberId);
        return res;
    }

    async searchByBusinessIdUseDataloader(
        businessId: number,
    ): Promise<Appointment[]> {
        const res = await this.findByBusinessIdDataloader.load(businessId);
        return res;
    }

    async getLastAppointment(
        appointments: Appointment[],
        businessId?: number,
    ): Promise<Appointment | null> {
        appointments = appointments.filter(
            (data) =>
                moment(data.date).isBefore(moment()) &&
                data.status == EnumAppointmentStatus.Register &&
                data.businessId == businessId,
        );

        if (appointments.length) {
            appointments = _.orderBy(appointments, ['date'], ['desc']);
            const appointmentServiceItems =
                await this.appointmentServiceItemService.searchByAppointmentIdUseDataloader(
                    appointments[0].id,
                );
            appointments[0].appointmentServiceItems = appointmentServiceItems;
            return appointments[0];
        }

        return null;
    }

    async getNextAppointment(
        appointments: Appointment[],
        businessId?: number,
    ): Promise<Appointment | null> {
        const appointment = appointments.find(
            (data) =>
                moment(data.date).isAfter(moment()) &&
                data.businessId == businessId,
        );

        if (appointment) {
            const appointmentServiceItems =
                await this.appointmentServiceItemService.searchByAppointmentIdUseDataloader(
                    appointment.id,
                );
            appointment.appointmentServiceItems = appointmentServiceItems;
            return appointment;
        }

        return null;
    }

    async findShoppingCenterAppointmentIsExists(
        body: FindShoppingCenterAppointmentParams,
    ): Promise<void> {
        const appointmentCount = await db.Appointment.count({
            where: {
                store_id: body.storeId,
                time: body.time,
                date: body.appointmentDate,
                time_type: body.timeType,
                is_confirmed: true,
                status: {
                    [Op.ne]: EnumAppointmentStatus.Cancel,
                },
            },
        });
        if (appointmentCount > 0) {
            throw new httpErrors.BadRequest('該門市該時段已有預約,請重新選擇');
        }
    }

    async search(
        params: ISearchAppointmentParams,
    ): Promise<ISearchResult<Appointment[]>> {
        const filters: any = { ...params };

        if (params.companyId) {
            const stores = await this.storeService.findAllByCompany(
                params.companyId,
            );
            const storeIds: number[] = [];
            if (params.storeId) {
                const store = stores.rows.find(
                    (data) => data.id === params.storeId,
                );
                if (store) {
                    storeIds.push(store.id);
                }
            } else {
                stores.rows.map((data) => storeIds.push(data.id));
            }
            filters.storeIds = storeIds;
        }
        if (params.appointmentItemCode) {
            const appointmentItem =
                await this.appointmentItemService.findOneByCode(
                    params.appointmentItemCode,
                    params.regionId,
                );
            if (!appointmentItem) {
                return {
                    rows: [],
                    count: 0,
                };
            }
            filters.appointmentItemId = appointmentItem.id;
        }

        const result = await this.appointmentLibService.search(filters);

        return result;
    }

    async nextUnregisteredAppointmentByMemberId(
        memberId: number,
        regionId: number,
        notAppointmentServiceItemCode?: EnumAppointmentServiceItem,
        appointmentServiceItemCodes?: EnumAppointmentServiceItem[],
    ): Promise<Appointment | null> {
        const params: ISearchAppointmentLibParams = {
            memberId,
            date1: moment().format('YYYY-MM-DD'),
            status: EnumAppointmentStatus.Unregistered,
            sorts: [
                {
                    key: AppointmentSort.Date,
                    sort: EnumSortType.Ascend,
                },
                {
                    key: AppointmentSort.Time,
                    sort: EnumSortType.Ascend,
                },
            ],
            limit: 1,
            regionId,
        };

        if (notAppointmentServiceItemCode) {
            params.notAppointmentServiceItemCode =
                notAppointmentServiceItemCode;
        }

        if (appointmentServiceItemCodes) {
            params.appointmentServiceItemCodes = appointmentServiceItemCodes;
        }

        const result = await this.appointmentLibService.search(params);
        return result.count > 0 ? result.rows[0] : null;
    }

    async createBookingReturnAppointmentAndItems(
        params: ICreateAppointmentAndItemsParams,
        bookingId: number,
        tx: Transaction,
    ): Promise<Appointment> {
        /** 電話確認 */
        let isConfirmed = true;
        /** 前一天提醒 */
        let isReminded = false;
        /** 預約狀態 */
        let status = EnumAppointmentStatus.Unregistered;

        switch (params.dataSource) {
            case EnumAppointmentDataSource.Counter:
                isReminded = true; // 不用前一天提醒
                status = EnumAppointmentStatus.Register; // 不用報到

                break;
            case EnumAppointmentDataSource.Official:
                isConfirmed = false; // 必須電話確認

                break;
            default: {
                const appointmentDate = Helpers.Date.formatDate(
                    Helpers.Date.parse(params.appointmentDate),
                );
                // 預約時間在明天前
                if (
                    moment(appointmentDate).isSameOrBefore(
                        moment().add(1, 'day'),
                    )
                ) {
                    isReminded = true; // 不用前一天提醒
                }
            }
        }
        const newAppointment = await this.appointmentLibService.create(
            {
                ...params,
                isConfirmed,
                isReminded,
                status,
            },
            tx,
        );

        for (const itemAndSubitemIds of params.appointmentItemAndSubitemInput) {
            await this.appointmentServiceItemService.create(
                {
                    appointmentId: newAppointment.id,
                    appointmentItemId: itemAndSubitemIds.appointmentItemId,
                    appointmentSubitemIds:
                        itemAndSubitemIds.appointmentSubItemIds,
                },
                tx,
            );
        }

        //Booking補上appointment2Id
        const updateBooking: IUpdateBookingInput = {
            id: bookingId,
            updatedUserId: params.createdUserId,
            returnAppointmentId: newAppointment.id,
            regionId: params.regionId,
        };
        await this.bookingService.update(updateBooking, { tx });

        if (newAppointment.audiologistId) {
            await this.notificationService.create(
                {
                    userId: newAppointment.audiologistId,
                    message: `您有一個新的預約，客戶： ${newAppointment.name}`,
                    type: EnumNotificationType.Remind,
                    values: {
                        path: `/appointment/detail?id=${newAppointment.id}`,
                    },
                },
                { tx },
            );
        }
        return newAppointment;
    }

    async createAppointmentAndItems(
        params: ICreateAppointmentAndItemsParams,
    ): Promise<Appointment> {
        let newAppointmentId: number;
        const tx = await db.sequelize.transaction();
        try {
            /** 電話確認 */
            let isConfirmed = true;
            /** 前一天提醒 */
            let isReminded = false;
            /** 預約狀態 */
            let status = EnumAppointmentStatus.Unregistered;

            switch (params.dataSource) {
                case EnumAppointmentDataSource.Counter:
                    isReminded = true; // 不用前一天提醒
                    status = EnumAppointmentStatus.Register; // 不用報到
                    break;
                case EnumAppointmentDataSource.Official:
                case EnumAppointmentDataSource.Social:
                case EnumAppointmentDataSource.ClinicoSound:
                case EnumAppointmentDataSource.iCare:
                case EnumAppointmentDataSource.CochlearImplant:
                    isConfirmed = false; // 必須電話確認
                    break;
                default: {
                    const appointmentDate = Helpers.Date.formatDate(
                        Helpers.Date.parse(params.appointmentDate),
                    );
                    // 預約時間在明天前
                    if (
                        moment(appointmentDate).isSameOrBefore(
                            moment().add(1, 'day'),
                        )
                    ) {
                        isReminded = true; // 不用前一天提醒
                    }
                }
            }

            const newAppointment = await this.appointmentLibService.create(
                {
                    ...params,
                    isConfirmed,
                    isReminded,
                    status,
                },
                tx,
            );
            newAppointmentId = newAppointment.id;

            await this.validate(newAppointment);

            for (const itemAndSubitemIds of params.appointmentItemAndSubitemInput) {
                await this.appointmentServiceItemService.create(
                    {
                        appointmentId: newAppointment.id,
                        appointmentItemId: itemAndSubitemIds.appointmentItemId,
                        appointmentSubitemIds:
                            itemAndSubitemIds.appointmentSubItemIds,
                    },
                    tx,
                );
            }
            //不判斷預約項目只要有租借資料輸入就建立租借(Booking)
            if (params.rentAppointmentBookingCreateInput) {
                for (const device of params.rentAppointmentBookingCreateInput
                    .bookingRentDevices) {
                    const appointments = await this.findAllActiveBookings(
                        newAppointment.mobile!,
                        device.deviceType,
                        newAppointment.regionId,
                        params.rentAppointmentBookingCreateInput.storeId ??
                            newAppointment.storeId,
                    );
                    if (appointments.length > 0) {
                        throw new httpErrors.BadRequest(
                            `已重複預約[${appointments[0].store?.name},${appointments[0].date}]`,
                        );
                    }
                }

                const createBooking: ICreateBookingInput = {
                    rentAppointmentId: newAppointment.id,
                    storeId:
                        params.rentAppointmentBookingCreateInput.storeId ??
                        params.storeId,
                    memberId: params.memberId as number,
                    startDate:
                        params.rentAppointmentBookingCreateInput.startDate,
                    bookingRentDevices:
                        params.rentAppointmentBookingCreateInput
                            .bookingRentDevices,
                    endDate: params.rentAppointmentBookingCreateInput.endDate,
                    createdUserId: params.createdUserId,
                    regionId: params.regionId,
                };

                if (
                    createBooking.startDate &&
                    newAppointment.date !== createBooking.startDate
                ) {
                    throw new httpErrors.BadRequest(
                        '預約起始日必須等於租借設備起始日',
                    );
                }
                await this.bookingService.create(createBooking, { tx });
            }
            await tx.commit();
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }

        const appointment = await this.appointmentLibService.findOneOrError(
            newAppointmentId,
        );
        let checkRentDevices: BookingDevice[] | undefined;
        if (appointment?.rentAppointmentBooking?.bookingDevices) {
            checkRentDevices =
                appointment.rentAppointmentBooking.bookingDevices;
        }

        // 如果建立時直接指定device id 建立一個應歸還pubsub
        if (checkRentDevices) {
            const checkSetDevice = await this.bookingService.checkSetDevice(
                checkRentDevices,
            );

            if (checkSetDevice.isHasSetDeviceId) {
                await pubsub.publish('MENUBADGES', {
                    userId: appointment.createdUserId,
                    storeId: appointment.storeId,
                    keys: ['bookingShouldReturned'],
                });
            }
        }

        // 預約一欄表即時更新
        await pubsub.publish('APPOINTMENTS', {
            type: EnumAppointmenSubscriptiontType.Created,
            appointment: appointment,
        });

        if (appointment.audiologistId) {
            await this.notificationService.create({
                userId: appointment.audiologistId,
                message: `您有一個新的預約，客戶： ${appointment.name}`,
                type: EnumNotificationType.Remind,
                values: {
                    path: `/appointment/detail?id=${appointment.id}`,
                },
            });
        }

        /**
         * Google Ads 更新名稱欄位
         * 廣告帳號Id(googleAdsCustomerId) 是查詢的必要資料
         */
        if (params.googleAdsCustomerId) {
            let googleAdsName = '';

            // Google Ads 活動查詢
            if (params.googleAdsCampaignId) {
                const GoogleCampaign = await this.googleAdsService.getCampaign({
                    customerId: params.googleAdsCustomerId,
                    campaignId: params.googleAdsCampaignId,
                });
                if (GoogleCampaign?.name) {
                    googleAdsName = googleAdsName.concat(
                        '',
                        GoogleCampaign.name,
                    );
                }
            }

            // Google Ads 廣告群組查詢
            if (params.googleAdsAdGroupId) {
                const AdGroup = await this.googleAdsService.getAdGroup({
                    customerId: params.googleAdsCustomerId,
                    adGroupId: params.googleAdsAdGroupId,
                });
                if (AdGroup?.name) {
                    googleAdsName = googleAdsName.concat('_', AdGroup.name);
                }
            }

            appointment.update({
                googleAdsName: googleAdsName ?? null,
            });
        }

        //設備租借(預約日和建立日同一天則不寄送)
        if (checkRentDevices && !moment().isSame(appointment.date, 'day')) {
            const set = new Set();
            const smsRentDeviceTypes = checkRentDevices.filter((item) =>
                !set.has(item.rentDeviceType)
                    ? set.add(item.rentDeviceType)
                    : false,
            );
            //官網來的預約發送還要確認時間的簡訊，後台預約的直接發送確認簡訊
            if (appointment.dataSource == EnumAppointmentDataSource.Official) {
                smsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForCreate(
                        appointment,
                        rentDevice.rentDeviceType,
                    );
                });
            } else {
                smsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForConfirmed(
                        appointment,
                        rentDevice.rentDeviceType,
                    );
                });
            }
        }

        const eventParams: ProcessPotentialTargetByAppointmentParams = {
            appointmentId: appointment.id,
            storeId: appointment.storeId,
            channelUserId: appointment.channelUserId,
            mobile: params.mobile,
            memberId: params.memberId,
            doctorReferralCode: params.doctorReferralCode,
        };
        this.potentialTargetEvent.emitter.emit(
            EVENTS.POTENTIAL_TARGET.APPOINTMENT.PROCESS,
            eventParams,
        );

        // 發送VIP會員預約郵件通知
        await this.appointmentEmailService.sendVipAppointmentEmail(appointment);

        return appointment;
    }

    async findAllActiveBookings(
        mobile: string,
        rentDeviceType: EnumRentDeviceType,
        regionId: number,
        storeId: number,
    ): Promise<Appointment[]> {
        const filters: any = {};
        const storefilters: any = {};

        filters.status = {
            [db.Op.ne]: EnumAppointmentStatus.Cancel,
        };
        filters.mobile = mobile;
        filters.regionId = regionId;
        const store = await this.storeService.findOneOrError(storeId);
        // 只判斷同通路
        if (store.companyId) {
            storefilters.companyId =
                await this.companyService.generateCompanyFilter(
                    store.companyId,
                );
        }
        const appointments = await db.Appointment.findAll({
            include: [
                {
                    model: db.Store,
                    where: storefilters,
                },
                {
                    model: db.Booking,
                    as: 'rentAppointmentBooking',
                    include: [
                        {
                            model: db.BookingDevice,
                            required: true,
                            where: { rentDeviceType: rentDeviceType },
                        },
                    ],
                    where: {
                        status: {
                            [db.Op.in]: [
                                EnumBookingStatus.UnReceive,
                                EnumBookingStatus.Received,
                            ],
                        },
                        deleted: false,
                    },
                    required: true,
                },
            ],
            where: filters,
        });
        return appointments;
    }

    async findTodayAppointmentsByStore(
        storeCode: string,
    ): Promise<Appointment[]> {
        const store = await this.storeSvc.findOneOrErrorByCode(storeCode);

        const appointments = await db.Appointment.findAll({
            include: [
                {
                    model: db.AppointmentServiceItem,
                    required: true,
                    include: [
                        {
                            model: db.AppointmentItem,
                            required: true,
                        },
                    ],
                },
            ],
            where: {
                is_confirmed: true,
                storeId: store.id,
                status: {
                    [Op.ne]: EnumAppointmentStatus.Cancel,
                },
                date: {
                    [Op.gte]: moment().startOf('day').toDate(),
                    [Op.lte]: moment().endOf('day').toDate(),
                },
            },
        });
        return appointments;
    }

    async findServiceHoursByCodes(
        code: string,
    ): Promise<AppointmentItem | null> {
        const appointmentItem = await db.AppointmentItem.findOne({
            where: {
                code: code,
            },
        });

        return appointmentItem;
    }

    // 時間加總工具函數（考慮小數點進位）
    addHoursToTime(time: string | undefined, hours: number): string {
        // 如果 time 為 undefined，使用預設時間 '0000'
        const validTime = time || '0000';
        let hour = 0,
            minute = 0;

        if (validTime) {
            if (validTime.includes(':')) {
                [hour, minute] = validTime.split(':').map(Number);
            } else if (/^\d{4}$/.test(validTime)) {
                hour = Number(validTime.slice(0, 2));
                minute = Number(validTime.slice(2));
            }
        }

        const date = new Date();
        date.setHours(hour, minute, 0, 0);
        date.setTime(date.getTime() + hours * 60 * 60 * 1000);

        // 直接輸出 HHmm 格式
        return `${_.padStart(date.getHours().toString(), 2, '0')}${_.padStart(
            date.getMinutes().toString(),
            2,
            '0',
        )}`;
    }

    sortShoopingCenterAppointmentTime(
        appointments: {
            time1: string | undefined;
            time2: string | undefined;
        }[],
    ) {
        return appointments.sort((a, b) => {
            if (a.time1 && b.time1) {
                const timeA = parseInt(a.time1);
                const timeB = parseInt(b.time1);
                return timeA - timeB; // 升冪排序
            }
            return 0; // 如果 time1 為 undefined，則不改變順序
        });
    }

    // 計算時間差小於指定分鐘數的預約區段
    calculateShortIntervals(
        appointments: {
            time1: string | undefined;
            time2: string | undefined;
        }[],
        timeDiffMinutes: number | undefined,
    ) {
        // 排序預約時間
        // 過濾掉 time1 為 undefined 的預約
        const validAppointments = appointments.filter(
            (appointment) => appointment.time1 !== undefined,
        );

        // 排序預約時間（轉換成數字後比較）
        this.sortShoopingCenterAppointmentTime(validAppointments);

        const shortIntervals: {
            time1: string | undefined;
            time2: string | undefined;
        }[] = [];
        for (let i = 0; i < validAppointments.length - 1; i++) {
            const currentTime2 = this.convertToDateHHMM(
                validAppointments[i].time2,
            );
            const nextTime1 = this.convertToDateHHMM(
                validAppointments[i + 1].time1,
            );

            // 新增判斷條件
            if (currentTime2 && nextTime1 && timeDiffMinutes !== undefined) {
                const diffMinutes =
                    (nextTime1.getTime() - currentTime2.getTime()) /
                    (1000 * 60);

                if (
                    diffMinutes < timeDiffMinutes * 60 &&
                    diffMinutes !== timeDiffMinutes * 60 &&
                    nextTime1.getTime() > currentTime2.getTime()
                ) {
                    shortIntervals.push({
                        time1: validAppointments[i].time2,
                        time2: validAppointments[i + 1].time1,
                    });
                }
            }
        }
        return shortIntervals;
    }

    // 輔助函式，將 HHMM 字串轉換為 Date 物件
    convertToDateHHMM(time: string | undefined): Date | undefined {
        if (time === undefined) {
            return undefined; // 如果 time 為 undefined，返回 undefined
        }

        if (time.length !== 4 || isNaN(parseInt(time))) {
            return undefined; //如果time的長度不是4或是time不能轉換成數字，也返回undefined
        }

        const now = new Date();
        const hours = parseInt(time.slice(0, 2));
        const minutes = parseInt(time.slice(2, 4));

        // 檢查轉換後的 hours 和 minutes 是否為 NaN
        if (isNaN(hours) || isNaN(minutes)) {
            return undefined;
        }

        return new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
            hours,
            minutes,
        );
    }
    async getTodayAppointmentsByStoreAndCalculateServiceItemTime(
        params: ShoppingCenterAppointmentReqDTO,
    ) {
        const { storeCode, serviceItemCode } = params;
        const todayAppointments = await this.findTodayAppointmentsByStore(
            storeCode,
        );
        const result = todayAppointments.map((todayAppointment) => {
            // 計算總 serviceHours
            const totalServiceHours = _.sumBy(
                todayAppointment.appointmentServiceItems,
                (asi) => asi.appointmentItem.serviceHours || 0,
            );

            // const appointment = todayAppointment.toJSON();

            return {
                // ...appointment,
                time1: todayAppointment.time,
                time2: this.addHoursToTime(
                    todayAppointment.time,
                    totalServiceHours,
                ), // 計算 time2
            };
        });

        const serviceItem = await this.findServiceHoursByCodes(serviceItemCode);
        const timeDiffMinutes = serviceItem?.serviceHours;
        const returnResults = [
            ...result,
            ...this.calculateShortIntervals(result, timeDiffMinutes),
        ];
        this.sortShoopingCenterAppointmentTime(returnResults);
        return returnResults;
    }

    async updateBusinessByUpdateAppointment(
        originalAppointment: db.Appointment,
        params: IUpdateAppointmentAndItemsParams,
        tx: Transaction,
    ) {
        const updatedUserId = params.updatedUserId;
        const businessId: number | null | undefined =
            params.businessId ?? originalAppointment.businessId;
        if (!businessId) {
            return;
        }
        const updateBusinessParams: any = _.pick(params, [
            'usingOpportunityId',
            'dealOpportunityId',
            'expectedClosedDate',
        ]);
        if (Object.keys(updateBusinessParams).length == 0) {
            return;
        }
        const business = await this.businessService.update(
            {
                id: businessId,
                ...updateBusinessParams,
                updatedUserId,
            },
            { tx },
        );
        return business;
    }

    async updateAppointmentAndItems(
        params: IUpdateAppointmentAndItemsParams,
    ): Promise<IAppointmentPublish> {
        const originalAppointment =
            await this.appointmentLibService.findOneOrError(params.id);
        let originalRentDevices: BookingDevice[] | undefined;

        if (originalAppointment?.rentAppointmentBooking?.bookingDevices) {
            originalRentDevices =
                originalAppointment.rentAppointmentBooking.bookingDevices;
        }

        //是否已設定租借設備
        let isHasSetDeviceId = false;
        if (originalRentDevices) {
            const checkSetDevice = await this.bookingService.checkSetDevice(
                originalRentDevices,
            );
            isHasSetDeviceId = checkSetDevice.isHasSetDeviceId;

            //有租借單且已領取設備不可變更門市資料
            if (isHasSetDeviceId) {
                //有租借單
                if (
                    params.storeId &&
                    originalAppointment.storeId != params.storeId
                ) {
                    throw new httpErrors.BadRequest(
                        '已領用租借設備不可變更門市',
                    );
                }
                if (
                    params.memberId &&
                    originalAppointment.memberId != params.memberId
                ) {
                    throw new httpErrors.BadRequest(
                        '已領用租借設備不可變更租借會員',
                    );
                }
            }
        }

        //觸發取消
        const isCancel =
            originalAppointment.status != EnumAppointmentStatus.Cancel &&
            params.status == EnumAppointmentStatus.Cancel;

        //觸發回復取消
        const isRevert =
            originalAppointment.status == EnumAppointmentStatus.Cancel &&
            params.status == EnumAppointmentStatus.Unregistered;
        if (isRevert) {
            params.cancelReasonType = null;
        }

        // 服務歷程為已歸還時，需要更新歸還預約的狀態
        if (params.serviceStatus === EnumAppointmentServiceStatus.Return) {
            params.returnAppointmentBookingUpdateInput = {
                ...params.returnAppointmentBookingUpdateInput,
                status: EnumBookingStatus.Returned,
            };
        }

        const tx = await db.sequelize.transaction();
        try {
            await this.updateBusinessByUpdateAppointment(
                originalAppointment,
                params,
                tx,
            );

            const appointment = await this.appointmentLibService.update(
                params,
                tx,
            );

            if (params.appointmentItemAndSubitemInput) {
                await this.appointmentServiceItemService.removeAll(
                    appointment.id,
                    tx,
                );
                for (const itemAndSubitemIds of params.appointmentItemAndSubitemInput) {
                    await this.appointmentServiceItemService.create(
                        {
                            appointmentId: appointment.id,
                            appointmentItemId:
                                itemAndSubitemIds.appointmentItemId,
                            appointmentSubitemIds:
                                itemAndSubitemIds.appointmentSubItemIds,
                        },
                        tx,
                    );
                }
            }

            if (params.logisticsRecipientInfoInput) {
                await this.appointmentLogisticsRecipientInfoRepo.update(
                    {
                        ...params.logisticsRecipientInfoInput,
                        appointmentId: appointment.id,
                        updatedUserId: params.updatedUserId,
                    },
                    tx,
                );
            }

            if (params.appointmentReferralInput) {
                await this.appointmentReferralUseCase.createOrUpdate(
                    {
                        ...params.appointmentReferralInput,
                        appointmentId: appointment.id,
                        userId: params.updatedUserId,
                    },
                    tx,
                );
            }

            // 恢復取消只更新預約狀態，Booking需重新新增
            const isCreateOrUpdateBooking =
                originalAppointment.status != EnumAppointmentStatus.Cancel;
            if (isCreateOrUpdateBooking) {
                await this.createOrUpdateBooking(
                    {
                        ...params,
                        appointment,
                        isHasSetDeviceId,
                    },
                    tx,
                );
            }

            // 當dataSource為iCare社群，且有填會員時，需更新icare與社群用戶關聯
            if (
                appointment?.appointmentDataSource?.id ==
                    EnumAppointmentDataSource.iCare &&
                appointment?.memberId &&
                appointment?.channelUserId
            ) {
                await this.iCareTestSocialUserReferralService.updateICareMemberSocialUserRelation(
                    tx,
                    {
                        memberId: appointment.memberId,
                        channelUserId: appointment.channelUserId,
                    },
                );
            }

            await this.validate(appointment);
            await tx.commit();
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }

        const updatedAppointment =
            await this.appointmentLibService.findOneOrError(
                originalAppointment.id,
            );

        // 更新後的appointment RenDevice
        let updateRentDevices: BookingDevice[] | undefined;
        if (updatedAppointment?.rentAppointmentBooking?.bookingDevices) {
            updateRentDevices =
                updatedAppointment.rentAppointmentBooking.bookingDevices;
        }

        if (updatedAppointment.audiologistId) {
            await this.notificationService.create({
                userId: updatedAppointment.audiologistId,
                message: `您有一個被修改的預約，客戶： ${updatedAppointment.name}`,
                type: EnumNotificationType.Remind,
                values: {
                    key: EnumNotificationActionComponent.Appointment,
                    payload: {
                        appointmentId: updatedAppointment.id,
                    },
                },
            });
        }

        // 預約一欄表即時更新, 更新前的預約
        await pubsub.publish('APPOINTMENTS', {
            type: EnumAppointmenSubscriptiontType.Deleted,
            appointment: originalAppointment,
        });

        // 預約一欄表即時更新, 更新後的預約
        await pubsub.publish('APPOINTMENTS', {
            type: EnumAppointmenSubscriptiontType.Created,
            appointment: updatedAppointment,
        });

        //設備租借(預約日和建立日同一天則不寄送)
        if (
            updateRentDevices &&
            !moment().isSame(updatedAppointment.date, 'day')
        ) {
            // 同種類的設備只需要發一封簡訊
            const set = new Set();
            const updateSmsRentDeviceTypes = updateRentDevices.filter((item) =>
                !set.has(item.rentDeviceType)
                    ? set.add(item.rentDeviceType)
                    : false,
            );
            if (
                !originalAppointment.isReminded &&
                updatedAppointment.isReminded
            ) {
                updateSmsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForConfirmed(
                        updatedAppointment,
                        rentDevice.rentDeviceType,
                    );
                });
            } else if (
                originalAppointment.storeId != updatedAppointment.storeId ||
                originalAppointment.date != updatedAppointment.date ||
                originalAppointment.time != updatedAppointment.time
            ) {
                updateSmsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForUpdate(
                        updatedAppointment,
                        rentDevice.rentDeviceType,
                    );
                });
            }
        }

        // 原本有租借更新後沒有租借才觸發取消簡訊，不然走update簡訊即可
        if (originalRentDevices && !updateRentDevices) {
            const set = new Set();

            // 同種類的設備只需要發一封簡訊
            const originalSmsRentDeviceTypes = originalRentDevices.filter(
                (item) =>
                    !set.has(item.rentDeviceType)
                        ? set.add(item.rentDeviceType)
                        : false,
            );

            if (
                isCancel &&
                updatedAppointment.cancelReasonType ==
                    EnumAppointmentCancelReasonType.CustomerCancel
            ) {
                originalSmsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForCustomerCancel(
                        originalAppointment,
                        rentDevice.rentDeviceType,
                    );
                });
            } else if (isCancel) {
                originalSmsRentDeviceTypes.forEach((rentDevice) => {
                    this.bookingSMSService.sendSMSForCancel(
                        originalAppointment,
                        rentDevice.rentDeviceType,
                    );
                });
            }
        }

        const publishKeys: (keyof MenuBadge)[] = ['appointmentConfirmedNotYet'];

        if (updateRentDevices) {
            const checkSetDevice = await this.bookingService.checkSetDevice(
                updateRentDevices,
            );

            if (checkSetDevice.isHasSetDeviceId) {
                publishKeys.push('bookingShouldReturned');
            }
        }

        const evetParams: ProcessPotentialTargetByAppointmentParams = {
            appointmentId: updatedAppointment.id,
            storeId: updatedAppointment.storeId,
            channelUserId:
                updatedAppointment.channelUserId ?? params.channelUserId,
            mobile: updatedAppointment.mobile,
            memberId: updatedAppointment.member?.id,
            doctorReferralCode: params.doctorReferralCode,
        };
        this.potentialTargetEvent.emitter.emit(
            EVENTS.POTENTIAL_TARGET.APPOINTMENT.PROCESS,
            evetParams,
            EnumProcessStatus.UPDATE,
        );
        // 預約取消
        if (
            isCancel &&
            originalAppointment.appointmentServiceItems.find((value) =>
                Object.values(EnumAppointmentServiceItemSendEmail).some(
                    (item) => value.appointmentItem.code.includes(item),
                ),
            ) &&
            originalAppointment.dataSource == 2 // Official 官網預約
        ) {
            await this.appointmentEmailService.resmedOfficeCancelMail(
                originalAppointment,
            );
        }

        // 若更新預約狀態為已完成報告 ＆ 寄簡訊 #113187
        if (
            originalAppointment.status === EnumAppointmentStatus.Register &&
            updatedAppointment?.serviceStatus ===
                EnumAppointmentServiceStatus.Report
        ) {
            this.bookingSMSService.sendSMSForBookingReportRS(
                updatedAppointment,
            );
        }

        // 當dataSource為電子耳社群預約，需要執行額外處理流程
        if (
            updatedAppointment.appointmentDataSource.id ==
            EnumAppointmentDataSource.CochlearImplant
        ) {
            const serviceItem =
                await this.appointmentServiceItemService.findOneOrError(
                    updatedAppointment.appointmentServiceItems[0].id,
                );
            await this.appointmentCIService.updateAppointmentHandler(
                updatedAppointment,
                serviceItem,
                !!params.isConfirmed, // 是否觸發預約確認
                isCancel, // 是否觸發預約取消
            );
        }

        return {
            appointment: updatedAppointment,
            keys: publishKeys,
        };
    }

    async updateMember(params: {
        id: number;
        memberId: number;
        updatedUserId: number;
        regionId: EnumRegion;
    }) {
        const tx = await db.sequelize.transaction();
        try {
            const appointment = await this.appointmentLibService.update(
                params,
                tx,
            );

            const rentAppointmentBooking =
                await this.bookingService.findOneByRentAppointmentId(
                    appointment.id,
                );
            if (rentAppointmentBooking) {
                await this.bookingService.update(
                    {
                        id: rentAppointmentBooking.id,
                        memberId: params.memberId,
                        updatedUserId: params.updatedUserId,
                        regionId: params.regionId,
                    },
                    {
                        tx,
                    },
                );
            }

            await this.validate(appointment);
            await tx.commit();
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    private async createOrUpdateBooking(
        params: IUpdateAppointmentBooking,
        tx: Transaction,
    ): Promise<void> {
        if (
            params.rentAppointmentBookingUpdateInput &&
            params.returnAppointmentBookingUpdateInput
        ) {
            throw new httpErrors.BadRequest('不可同時更新租借預約及歸還預約');
        }

        const updateBookingInput =
            params.rentAppointmentBookingUpdateInput ??
            params.returnAppointmentBookingUpdateInput;

        const updateBooking =
            params.appointment.rentAppointmentBooking ??
            params.appointment.returnAppointmentBooking;

        if (params.appointment.status !== EnumAppointmentStatus.Cancel) {
            if (updateBooking) {
                await this.validateBooking(params, updateBooking);

                if (updateBookingInput?.bookingRentDevices) {
                    let returnAppointmentDate = updateBooking.endDate;
                    if (updateBookingInput.endDate) {
                        returnAppointmentDate = updateBookingInput.endDate;
                    }

                    const returnAppointmentItemAndSubitemInput: AppointmentItemAndSubitemInput[] =
                        [];
                    let hasResmedDevice = false;
                    let hasHADevice = false;
                    let isSetDeviceId = false;
                    updateBookingInput.bookingRentDevices.forEach((row) => {
                        if (
                            row.deviceType == EnumRentDeviceType.HST ||
                            EnumRentDeviceType.PAP
                        ) {
                            hasResmedDevice = true;
                        }
                        if (row.deviceType == EnumRentDeviceType.HEARINGAID) {
                            hasHADevice = true;
                        }
                        if (!_.isNull(row.deviceId)) {
                            isSetDeviceId = true;
                        }
                    });

                    const subitemIds: number[] = [];
                    if (hasResmedDevice) {
                        const subitem =
                            await this.appointmentSubitemService.findOneByCode(
                                'return.resmed',
                            );
                        if (subitem) {
                            subitemIds.push(subitem.id);
                        }
                    }
                    if (hasHADevice) {
                        const subitem =
                            await this.appointmentSubitemService.findOneByCode(
                                'return.HA',
                            );
                        if (subitem) {
                            subitemIds.push(subitem.id);
                        }
                    }

                    const bookingServiceItem =
                        await this.appointmentItemService.findOneOrErrorByCode(
                            EnumAppointmentServiceItem.Return,
                            params.regionId,
                        );
                    returnAppointmentItemAndSubitemInput.push({
                        appointmentItemId: bookingServiceItem.id,
                        appointmentSubItemIds: subitemIds,
                    });

                    //之前還沒有設定過設備第一次指定租借設備ID才建立歸還預約
                    if (!params.isHasSetDeviceId && isSetDeviceId) {
                        await this.createBookingReturnAppointmentAndItems(
                            {
                                regionId: params.appointment.regionId,
                                memberId: params.appointment.memberId,
                                memberType: params.appointment.memberType,
                                name: params.appointment.name,
                                storeId: params.appointment.storeId,
                                identity: params.appointment.identity,
                                timeType: params.appointment.timeType,
                                mobile: params.appointment.mobile!,
                                time: params.appointment.time!,
                                dataSource:
                                    EnumAppointmentDataSource.ClinicoStore,
                                serviceHours: 1,
                                appointmentDate: returnAppointmentDate,
                                appointmentItemAndSubitemInput:
                                    returnAppointmentItemAndSubitemInput,
                                gender: <EnumMemberGender>(
                                    params.appointment.gender
                                ),
                                createdUserId: params.updatedUserId,
                            },
                            updateBooking.id,
                            tx,
                        );
                    }
                }
                await this.updateAppointmentBooking(updateBooking, params, tx);
            } else if (params.rentAppointmentBookingUpdateInput) {
                //** 非租借改為租借(沒有Booking代表之前沒有租借) */
                await this.createAppointmentBooking(
                    params.appointment,
                    params,
                    tx,
                );
            }
        } else if (updateBooking) {
            await this.cancelAppointmentBooking(updateBooking, params, tx);
        }
    }

    private async validate(appointment: Appointment): Promise<void> {
        if (!appointment.mobile) {
            throw new httpErrors.BadRequest('預約必須要有主要聯絡電話');
        }
        if (!appointment.time) {
            throw new httpErrors.BadRequest('預約必須要預約時間');
        }
    }

    private async validateBooking(
        params: IUpdateAppointmentBooking,
        updateBooking: Booking | null,
    ): Promise<void> {
        const { isHasSetDeviceId } = params;

        const updateRentAppointmentBookingInput =
            params.rentAppointmentBookingUpdateInput;

        const updateReturnAppointmentBookingInput =
            params.returnAppointmentBookingUpdateInput;

        const updateBookingInput =
            updateRentAppointmentBookingInput ??
            updateReturnAppointmentBookingInput;

        const appointment = params.appointment;
        if (updateBooking) {
            if (
                !_.isEmpty(updateBooking.bookingDevices) &&
                updateBookingInput
            ) {
                if (
                    updateBookingInput.bookingRentDevices != undefined &&
                    updateBookingInput.bookingRentDevices &&
                    isHasSetDeviceId
                ) {
                    throw new httpErrors.BadRequest(
                        '請先歸還已領取設備才可重新選擇設備',
                    );
                }
            }
            let checkDevice: ICheckSetDevice = {
                isHasSetDeviceId: false,
                checkRentDevices: [],
            };
            if (updateBooking.bookingDevices) {
                checkDevice = await this.bookingService.checkSetDevice(
                    updateBooking.bookingDevices,
                );
            }

            if (checkDevice.isHasSetDeviceId && _.isNull(updateBookingInput)) {
                throw new httpErrors.BadRequest('已領取設備不可取消租借');
            }

            const checkEndDate = updateReturnAppointmentBookingInput?.endDate;
            if (checkEndDate && checkEndDate != appointment.date) {
                throw new httpErrors.BadRequest(
                    '歸還預約日期必須為租借設備日期(迄)',
                );
            }
            const checkStartDate = updateRentAppointmentBookingInput?.startDate;
            if (checkStartDate && checkStartDate != appointment.date) {
                throw new httpErrors.BadRequest(
                    '租借預約日期必須為租借設備日期(起)',
                );
            }

            if (
                updateBooking.returnAppointmentId &&
                !updateBooking.rentAppointmentId
            ) {
                throw new httpErrors.BadRequest(
                    `查無租借預約[bookingId:${updateBooking.id}]`,
                );
            }
            if (
                appointment.status == EnumAppointmentStatus.Cancel &&
                updateBooking.status == EnumBookingStatus.Received
            ) {
                throw new httpErrors.BadRequest('已領取租借設備不可取消預約');
            }
        }
    }

    private async createAppointmentBooking(
        appointment: Appointment,
        params: IUpdateAppointmentAndItemsParams,
        tx: Transaction,
    ): Promise<void> {
        if (
            appointment.date !==
            params.rentAppointmentBookingUpdateInput?.startDate
        ) {
            throw new httpErrors.BadRequest('預約起始日必須等於租借設備起始日');
        }
        if (!params.rentAppointmentBookingUpdateInput?.bookingRentDevices) {
            throw new httpErrors.BadRequest('必須要有租借設備資料');
        }
        if (!params.rentAppointmentBookingUpdateInput?.endDate) {
            throw new httpErrors.BadRequest('必須要有租借歸還日');
        }

        const createBooking: ICreateBookingInput = {
            rentAppointmentId: appointment.id,
            storeId:
                params.rentAppointmentBookingUpdateInput.storeId ??
                appointment.storeId,
            memberId: appointment.memberId,
            startDate: params.rentAppointmentBookingUpdateInput.startDate,
            bookingRentDevices:
                params.rentAppointmentBookingUpdateInput.bookingRentDevices,
            endDate: params.rentAppointmentBookingUpdateInput.endDate,
            createdUserId: params.updatedUserId,
            regionId: params.regionId,
        };

        await this.bookingService.create(createBooking, { tx });
    }

    private async updateAppointmentBooking(
        booking: Booking,
        params: IUpdateAppointmentAndItemsParams,
        tx: Transaction,
    ): Promise<Booking> {
        const updateBooking: IUpdateBookingInput = {
            id: booking.id,
            updatedUserId: params.updatedUserId,
            regionId: params.regionId,
        };
        //當預約有變更門市及會員Booking的資料要一起變更
        if (params.memberId) {
            updateBooking.memberId = params.memberId;
        }

        if (params.storeId) {
            updateBooking.storeId = params.storeId;
        }

        const updateBookingInput =
            params.rentAppointmentBookingUpdateInput ??
            params.returnAppointmentBookingUpdateInput;

        if (updateBookingInput) {
            if (updateBookingInput.bookingRentDevices) {
                updateBooking.bookingRentDevices =
                    updateBookingInput.bookingRentDevices;
            }
            if (updateBookingInput.startDate) {
                updateBooking.startDate = updateBookingInput.startDate;
            }
            if (updateBookingInput.endDate) {
                updateBooking.endDate = updateBookingInput.endDate;
            }
            if (updateBookingInput.status != undefined) {
                updateBooking.status = updateBookingInput.status;
            }
            if (updateBookingInput.storeId) {
                updateBooking.storeId = updateBookingInput.storeId;
            }
            return await this.bookingService.update(updateBooking, { tx });
        } else {
            //沒有歸還預約才能清空Booking
            if (!booking.returnAppointmentId) {
                await this.cancelAppointmentBooking(booking, params, tx);
            }
        }
        return booking;
    }

    private async cancelAppointmentBooking(
        booking: Booking,
        params: IUpdateAppointmentAndItemsParams,
        tx: Transaction,
    ): Promise<void> {
        const deleteBooking: IDeleteBooking = {
            id: booking.id,
            updatedUserId: params.updatedUserId,
        };
        await this.bookingService.delete(deleteBooking, { tx });
    }

    async createInternal(
        params: ICreateAppointmentInternalParams,
    ): Promise<Appointment> {
        const user = await this.userService.findOneOrErrorByCode('IT001');
        const store = await this.storeService.findOneOrErrorByCode(
            params.storeCode,
        );
        const appointmentItemAndSubitemInput: AppointmentItemAndSubitemInput[] =
            [];

        if (params.appointmentItemAndSubitemInput) {
            for (const appointmentItem of params.appointmentItemAndSubitemInput) {
                const item =
                    await this.appointmentItemService.findOneOrErrorByCode(
                        appointmentItem.appointmentItemCode,
                        EnumRegion.TW,
                    );

                let subItemIds: number[] | undefined = undefined;
                if (appointmentItem.appointmentSubItemCodes) {
                    subItemIds = [];
                    const subitems =
                        await this.appointmentSubitemService.findOneByCodes(
                            appointmentItem.appointmentSubItemCodes,
                        );
                    subItemIds = subItemIds.concat(
                        subitems.map((data) => data.id),
                    );
                }

                appointmentItemAndSubitemInput.push({
                    appointmentItemId: item.id,
                    appointmentSubItemIds: subItemIds,
                });
            }
        }

        const { hours } = await this.getAppointmentItemTime(
            params.time,
            appointmentItemAndSubitemInput.map(
                (data) => data.appointmentItemId,
            ),
        );
        const { memberType, member, channelUserId } =
            await this.checkMemberType({
                ...params,
                companyId: store.companyId,
            });

        const appointment = await this.createAppointmentAndItems({
            ...params,
            regionId: 1, // 預設台灣
            memberId: member ? member.id : undefined,
            serviceHours: hours,
            storeId: store.id,
            createdUserId: user.id,
            appointmentItemAndSubitemInput,
            memberType,
            gender: params.gender ? params.gender : <any>null,
            channelUserId: params.channelUserId ?? channelUserId,
        });

        return appointment;
    }

    private async checkMemberType(params: {
        memberType?: EnumAppointmentMemberType;
        channelUserId?: string;
        mobile: string;
        companyId: number;
        memberId?: number;
    }): Promise<{
        memberType: EnumAppointmentMemberType;
        member?: Member;
        channelUserId?: string;
    }> {
        if (params.memberType) {
            return { memberType: params.memberType };
        }

        try {
            // Check social user member if channelUserId exists
            if (params.channelUserId) {
                const socialUserMember = await this.socialUserMemberSvc
                    .getMemberByChannelUserId(
                        params.companyId,
                        params.channelUserId,
                    )
                    .catch(() => null);

                if (socialUserMember) {
                    return {
                        memberType: EnumAppointmentMemberType.Old,
                        member: socialUserMember,
                        channelUserId: params.channelUserId,
                    };
                }
            }

            let member: Member | null = null;
            // Find member by ID
            if (params.memberId) {
                member = await this.memberService.findOneById(params.memberId);
            } else {
                // Find member by mobile number
                member = await this.memberService.findOneByMobile(
                    params.mobile,
                    params.companyId,
                );
            }

            if (!member) {
                return { memberType: EnumAppointmentMemberType.New };
            }

            // Get channel ID for existing member
            const memberChannelId =
                await this.memberChannelIdSvc.findOneByMemberId(member.id);

            return {
                memberType: EnumAppointmentMemberType.Old,
                member,
                channelUserId: memberChannelId?.channelUserId,
            };
        } catch {
            return { memberType: EnumAppointmentMemberType.New };
        }
    }

    async updateInterInternal(
        params: IUpdateAppointmentInternalParams,
    ): Promise<IAppointmentPublish> {
        const user = await this.userService.findOneOrErrorByCode('IT001');
        const store = await this.storeService.findOneOrErrorByCode(
            params.storeCode,
        );
        let appointmentItemAndSubitemInput:
            | AppointmentItemAndSubitemInput[]
            | undefined = undefined;

        if (params.appointmentItemAndSubitemInput) {
            appointmentItemAndSubitemInput = [];
            for (const appointmentItem of params.appointmentItemAndSubitemInput) {
                const item =
                    await this.appointmentItemService.findOneOrErrorByCode(
                        appointmentItem.appointmentItemCode,
                        params.regionId,
                    );

                let subItemIds: number[] | undefined = undefined;
                if (appointmentItem.appointmentSubItemCodes) {
                    subItemIds = [];
                    const subitems =
                        await this.appointmentSubitemService.findOneByCodes(
                            appointmentItem.appointmentSubItemCodes,
                        );
                    subItemIds = subItemIds.concat(
                        subitems.map((data) => data.id),
                    );
                }

                appointmentItemAndSubitemInput.push({
                    appointmentItemId: item.id,
                    appointmentSubItemIds: subItemIds,
                });
            }
        }

        const { memberType, member, channelUserId } =
            await this.checkMemberType({
                ...params,
                companyId: store.companyId,
            });

        const appointment = await this.updateAppointmentAndItems({
            ...params,
            memberId: member ? member.id : undefined,
            storeId: store.id,
            updatedUserId: user.id,
            appointmentItemAndSubitemInput,
            memberType,
            gender: params.gender ? params.gender : <any>null,
            isConfirmed: false,
            regionId: params.regionId,
            channelUserId,
        });

        return appointment;
    }

    async cancelInternal(
        params: ICancelAppointmentInternalParams,
    ): Promise<void> {
        const user = await this.userService.findOneOrErrorByCode('IT001');

        const res = await this.updateAppointmentAndItems({
            id: params.id,
            status: EnumAppointmentStatus.Cancel,
            cancelReasonType: EnumAppointmentCancelReasonType.CustomerCancel,
            updatedUserId: user.id,
            regionId: params.regionId,
        });

        // 記錄退款中
        if (res.appointment.appointmentPaymentRecord) {
            await this.appointmentPaymentRecordService.cancel({
                appointmentId: res.appointment.id,
                status: EnumAppointmentPaymentStatus.Refund,
            });

            this.bookingSMSService.sendSMSForBookingCancelRS(
                res.appointment,
                res.appointment.appointmentPaymentRecord?.tradeCode,
            );
        }
    }

    async refundInternal(
        params: IRefundAppointmentInternalParams,
    ): Promise<void> {
        const appointmentPaymentRecord =
            await this.appointmentPaymentRecordService.findOneByTradeCode(
                params.tradeCode,
            );

        // 記錄退款完成
        if (appointmentPaymentRecord) {
            await this.appointmentPaymentRecordService.cancel({
                appointmentId: appointmentPaymentRecord.appointmentId,
                status: EnumAppointmentPaymentStatus.Refunded,
            });
            const appointment = await this.appointmentLibService.findOneOrError(
                appointmentPaymentRecord.appointmentId,
            );
            this.bookingSMSService.sendSMSForBookingRefundRS(appointment);
        }
    }

    async invoiceInternal(
        params: IInvoiceAppointmentInternalParams,
    ): Promise<void> {
        const appointmentPaymentRecord =
            await this.appointmentPaymentRecordService.findOneByTradeCode(
                params.tradeCode,
            );

        // 記錄發票
        if (appointmentPaymentRecord) {
            await this.appointmentPaymentRecordService.invoice({
                appointmentId: appointmentPaymentRecord.appointmentId,
                invoice: params.invoice,
            });
        }
    }

    private async getAppointmentItemTime(
        appointmentTime: string,
        appointmentIds?: number[],
    ): Promise<{ endTime: string; hours: number }> {
        let hours = 0;
        if (!appointmentTime) {
            throw new httpErrors.BadRequest('appointmentTime is required');
        }
        if (!Helpers.Date.parse(appointmentTime, 'HHmm').isValid()) {
            throw new httpErrors.BadRequest(
                'appointmentTime is not valid (HHmm)',
            );
        }
        if (!appointmentIds) {
            return {
                hours,
                endTime: appointmentTime,
            };
        }

        const appointmentItems = await this.appointmentItemService.search({
            appointmentIds: appointmentIds,
        });

        for (const appointmentItem of appointmentItems.rows) {
            hours += appointmentItem.serviceHours;
        }
        const endTime = Helpers.Date.parse(appointmentTime, 'HHmm').add(
            hours,
            'hours',
        );

        return {
            hours,
            endTime: endTime.format('HHmm'),
        };
    }

    private findByMemberIdDataloader = new DataLoader(
        async (memberIds: readonly number[]) => {
            const res = await db.Appointment.findAll({
                where: {
                    memberId: { [db.Op.in]: memberIds as number[] },
                },
                order: [
                    ['date', 'ASC'],
                    ['time', 'ASC'],
                ],
                raw: true,
            });
            const grouped = groupBy(res, 'memberId');
            return memberIds.map((key) => grouped[key] || null);
        },
        {
            cache: false,
            batchScheduleFn: (callback) => setTimeout(callback, 50),
        },
    );

    private findByBusinessIdDataloader = new DataLoader(
        async (businessIds: readonly number[]) => {
            const res = await db.Appointment.findAll({
                where: {
                    businessId: { [db.Op.in]: businessIds as number[] },
                },
                order: [
                    ['date', 'ASC'],
                    ['time', 'ASC'],
                ],
                raw: true,
            });
            const grouped = groupBy(res, 'businessId');
            return businessIds.map((key) => grouped[key] || null);
        },
        {
            cache: false,
            batchScheduleFn: (callback) => setTimeout(callback, 50),
        },
    );

    async findOneByIdAndMobile(
        id: number,
        mobile: string,
    ): Promise<Appointment | null> {
        const result = await this.appointmentLibService.search({
            id: id,
            mobile: mobile,
            regionId: 1,
        });
        const rows = result.rows;

        return rows.length == 1 ? rows[0] : null;
    }
}
