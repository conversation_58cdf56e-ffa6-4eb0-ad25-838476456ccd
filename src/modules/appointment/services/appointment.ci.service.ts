import { Helpers, Utils } from '@clinico/clinico-node-framework';
import {
    Appointment,
    AppointmentItem,
    AppointmentServiceItem,
    AppointmentSubitem,
} from '@clinico/clinico-persistence';
import { EnumCochlearImplantSocialUsersChannelType } from '@clinico/clinico-persistence/dist/models/cochlearImplantSocialUsers.model';
import axios, { AxiosInstance } from 'axios';
import * as httpErrors from 'http-errors';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Inject, Service } from 'typedi';
import configs from '@/configs';
import { CochlearImplantSocialUsersRepository } from '@/modules/cochlearImplant/cochlearImplantSocialUsers.repo';
import logger from '@/modules/logger/logger.service';
import { StoreGatewayUseCase } from '@/modules/store/store.gateway.usecase';
import { StoreService } from '@/modules/store/store.service';
import { ReqSocialBookingCochlearImplantBodyDTO } from '../appointment.rest.type';
import {
    EnumAppointmentServiceItem,
    EnumAppointmentServiceSubItem,
} from '../appointment.type';

@Service()
export class AppointmentCochlearImplantService {
    @Inject() private storeService: StoreService;
    @Inject() private storeGatewayUseCase: StoreGatewayUseCase;
    @Inject() private ciSocialUserRepo: CochlearImplantSocialUsersRepository;
    private axiosInstance: AxiosInstance;

    constructor() {
        this.axiosInstance = axios.create({
            baseURL: configs.chatwootGateway.url,
            // baseURL: 'http://127.0.0.1:3030',
        });
    }

    // 預約通知信箱
    private contactEmail = {
        dev: {
            tm: ['<EMAIL>'],
            sales: ['<EMAIL>'],
        },
        prod: {
            tm: ['<EMAIL>'],
            sales: ['<EMAIL>'],
        },
    };

    // 需要做推播通知的行動方案類型
    private pushNotifySubitemTypes = [
        EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI].Cleaning,
        EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI].Supplies,
        EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI].Repair,
    ];

    /**
     * 電子耳社群預約基本檢查
     * @param body
     * @returns
     */
    async validateForSocialAppointment(
        body: ReqSocialBookingCochlearImplantBodyDTO,
    ) {
        const {
            storeCode,
            appointmentDate,
            channelUserId,
            channelType,
            appointmentItemAndSubitems,
        } = body;

        // 檢查門市是否存在
        const store = await this.storeService.findOneOrErrorByCode(storeCode);
        // 檢查預約時間是否合法
        const daysUntilBookingDate =
            await this.storeGatewayUseCase.getDaysUntilBookingDateByStore({
                code: storeCode,
                storeId: store.id,
                daysUntilBooking: store.daysUntilBooking,
            });
        if (!moment(daysUntilBookingDate).isValid()) {
            throw new httpErrors.BadRequest(
                `get store available booking date fail (${daysUntilBookingDate})`,
            );
        }
        if (moment(appointmentDate).isBefore(moment(daysUntilBookingDate))) {
            throw new httpErrors.BadRequest(
                `appointment date(${appointmentDate}) is before minimum available booking date(${daysUntilBookingDate})`,
            );
        }

        appointmentItemAndSubitems.map((a) => {
            // 檢查預約項目是否為電子耳
            if (a.appointmentItemCode != EnumAppointmentServiceItem.CI)
                throw new httpErrors.BadRequest('invalid appointment item');
            // 如果行動方案包含購買耗材，則預約日至少需為D+10
            if (
                a.appointmentSubItemCodes?.includes(
                    EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI]
                        .Supplies,
                ) &&
                moment(appointmentDate).diff(moment(), 'days') < 10
            )
                throw new httpErrors.BadRequest(
                    'appointment date should at least D+10',
                );
        });

        // 檢查社群用戶是否存在
        const socialUser = await this.ciSocialUserRepo.searchOneOrError({
            channelType,
            channelUserId,
        });

        return socialUser;
    }

    /**
     * 寄送電子耳預約創建郵件通知
     * @param appointment
     * @param serviceItem
     * @returns
     */
    async sendAppointmentCreatedNotify(
        appointment: Appointment,
        serviceItem: AppointmentServiceItem,
    ) {
        // 電子耳提供的行動方案Enum
        const enumCISubItem =
            EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI];
        // 該預約的服務項目：人工電子耳相關服務(1HR)
        const appointmentItem = serviceItem.appointmentItem;

        // 該預約的行動方案：購買耗材/清潔除溼/維修
        const serviceSubitems = serviceItem.appointmentServiceSubitem;
        const appointmentSubitems = serviceSubitems.map(
            (s) => s.appointmentSubitem,
        );
        // 該預約的行動方案代號List
        const appointmentSubitemCodes = appointmentSubitems.map((i) => i.code);

        const contacts = Helpers.Env.isProduction()
            ? this.contactEmail.prod
            : this.contactEmail.dev;

        if (appointmentSubitemCodes.includes(enumCISubItem.Repair)) {
            // 當預約行動方案包含維修時，需要寄信給TM
            await this.sendAppointmentCreatedMail(
                appointment,
                appointmentItem,
                appointmentSubitems,
                contacts.tm,
                contacts.sales,
            );
        }

        // NOTICE: 後來說不用寄了，先保留程式
        // if (appointmentSubitemCodes.includes(enumCISubItem.Supplies)) {
        //     // 當預約行動方案包含購買耗材時，需要寄信給業務群組
        //     await this.sendAppointmentCreatedMail(
        //         appointment,
        //         appointmentItem,
        //         appointmentSubitems,
        //         contacts.sales,
        //     );
        // }
        return;
    }

    /**
     * 寄送電子耳預約創建郵件通知
     * @param appointment
     * @param appointmentItem
     * @param appointmentSubItems
     * @returns
     */
    async sendAppointmentCreatedMail(
        appointment: Appointment,
        appointmentItem: AppointmentItem,
        appointmentSubItems: AppointmentSubitem[],
        to: string[],
        cc?: string[],
    ) {
        const { store, member } = appointment;
        // 取得template
        const template = await Utils.HtmlTemplater.template(
            configs.templateFolder + '/email/appointment/ciCreated.html',
        );

        // 組出行動方案名稱
        const appointmentSubItemNames = appointmentSubItems
            .map((i) => i.name)
            .join('/');

        // template 內容
        const data = {
            storeName: store?.name,
            storeCode: store?.code,
            memberCode: member?.code,
            memberName: member?.name,
            appointmentId: appointment.id,
            appointmentDate: appointment.date,
            appointmentTime: appointment.time,
            appointmentService: `${appointmentItem.name} ${appointmentSubItemNames}`,
        };
        Utils.Mailer.send({
            to: to.toString(),
            cc: cc?.toString(),
            subject: `${store?.name ?? ''} 電子耳服務預約通知`,
            body: template(data),
        });
        return;
    }

    /**
     * 電子耳社群預約更新處理流程
     * @param appointment
     * @param serviceItem
     * @param isConfirmed 是否觸發預約確認
     * @param isCancel 是否觸發預約取消
     * @returns
     */
    async updateAppointmentHandler(
        appointment: Appointment,
        serviceItem: AppointmentServiceItem,
        isConfirmed: boolean,
        isCancel: boolean,
    ) {
        // 電子耳提供的行動方案Enum
        const enumCISubItem =
            EnumAppointmentServiceSubItem[EnumAppointmentServiceItem.CI];
        // 該預約的服務項目：人工電子耳相關服務(1HR)
        const appointmentItem = serviceItem.appointmentItem;
        const serviceSubitems = serviceItem.appointmentServiceSubitem;
        // 該預約的行動方案：購買耗材/清潔除溼/維修
        const appointmentSubitems = serviceSubitems.map(
            (s) => s.appointmentSubitem,
        );
        // 該預約的行動方案代號List
        const appointmentSubitemCodes = appointmentSubitems.map((i) => i.code);

        // 檢查是否為需要通知的類型
        const isNotifyType =
            _.intersection(this.pushNotifySubitemTypes, appointmentSubitemCodes)
                .length > 0;
        if (!isNotifyType) return;

        if (isConfirmed && appointment.member) {
            // 預約確認時要推播line通知給用戶
            await this.sendAppointmentConfirmedLineNotify(
                appointment,
                appointmentSubitems,
            );
            return;
        }

        if (isCancel && appointment.member) {
            // 預約取消時要推播line通知給用戶
            await this.sendAppointmentCanceledLineNotify(
                appointment,
                appointmentSubitems,
            );
            return;
        }

        // NOTICE: 後來說不用寄了，先保留程式
        // if (
        //     appointmentSubitemCodes.includes(enumCISubItem.Cleaning) &&
        //     isCancel
        // ) {
        //     // 清潔預約取消時要發送mail通知
        //     await this.sendCleaningAppointmentCancelMail(
        //         appointment,
        //         appointmentItem,
        //         appointmentSubitems,
        //     );
        //     return;
        // }

        return;
    }

    /**
     * 寄送電子耳清潔預約取消郵件通知
     * @param appointment
     * @param appointmentItem
     * @param appointmentSubItems
     * @returns
     */
    async sendCleaningAppointmentCancelMail(
        appointment: Appointment,
        appointmentItem: AppointmentItem,
        appointmentSubItems: AppointmentSubitem[],
    ) {
        const { store, member } = appointment;
        // 取得template
        const template = await Utils.HtmlTemplater.template(
            configs.templateFolder + '/email/appointment/ciCanceled.html',
        );
        const contacts = Helpers.Env.isProduction()
            ? this.contactEmail.prod
            : this.contactEmail.dev;
        // 組出行動方案名稱
        const appointmentSubItemNames = appointmentSubItems
            .map((i) => i.name)
            .join('/');

        // template 內容
        const data = {
            storeName: store?.name,
            storeCode: store?.code,
            memberCode: member?.code,
            memberName: member?.name,
            appointmentId: appointment.id,
            appointmentDate: appointment.date,
            appointmentTime: appointment.time,
            appointmentService: `${appointmentItem.name} ${appointmentSubItemNames}`,
        };
        Utils.Mailer.send({
            // 取消要通知tm team及業務部門
            to: [...contacts.tm, ...contacts.sales].toString(),
            subject: `${store?.name ?? ''} 電子耳服務預約取消通知`,
            body: template(data),
        });

        return;
    }

    /**
     * 推播電子耳預約成功line通知
     * @param appointment
     * @param appointmentItem
     * @param appointmentSubItems
     * @returns
     */
    async sendAppointmentConfirmedLineNotify(
        appointment: Appointment,
        appointmentSubItems: AppointmentSubitem[],
    ) {
        try {
            // 取得社群會員資訊
            const { rows } = await this.ciSocialUserRepo.search({
                memberId: appointment.memberId,
                channelType: EnumCochlearImplantSocialUsersChannelType.LINE,
            });
            const socialUser = rows[0];
            // 沒有找到社群會員，跳過發送
            if (!socialUser || !socialUser.channelUserId) return;
            // 組出訊息內容
            const appointmentSubItemNames = appointmentSubItems
                .map((i) => i.name)
                .join('/');

            const weekday = moment(appointment.date)
                .locale('zh-tw')
                .format('dddd')
                .substring(2);
            const message = [
                '【預約成功】',
                `感謝您的預約，我們已為您安排好[${appointmentSubItemNames}]服務，以下為預約資訊:`,
                `預約時間: ${appointment.date} (${weekday}) ${appointment.time}`,
                `預約門市: ${appointment.store?.name}`,
                `預約地址: ${appointment.store?.address}`,
                `聯絡電話: ${appointment.store?.phone}`,
                '若您需要調整或有任何不便，請隨時來電聯繫我們。期待與您相見。',
            ].join('\n');

            // 請求 message-integration-gateway 發送 line 通知
            const endpoint = '/clinico/cochlear-implant/line/message';
            const response = await this.axiosInstance.post(endpoint, {
                channelUserId: socialUser.channelUserId,
                messages: [message],
            });
            if (response.status != 200 || response.data?.success != true) {
                throw new httpErrors.BadRequest(
                    `send cochlear implant line message fail: ${response.data}`,
                );
            }
            return;
        } catch (err) {
            const errResp = err?.response?.data?.error?.message ?? err;
            logger.info(`發送電子耳line推播通知失敗，原因：${errResp}`);
            throw errResp;
        }
    }

    /**
     * 推播電子耳預約取消line通知
     * @param appointment
     * @param appointmentItem
     * @param appointmentSubItems
     * @returns
     */
    async sendAppointmentCanceledLineNotify(
        appointment: Appointment,
        appointmentSubItems: AppointmentSubitem[],
    ) {
        try {
            // 取得社群會員資訊
            const { rows } = await this.ciSocialUserRepo.search({
                memberId: appointment.memberId,
                channelType: EnumCochlearImplantSocialUsersChannelType.LINE,
            });
            const socialUser = rows[0];
            // 沒有找到社群會員，跳過發送
            if (!socialUser || !socialUser.channelUserId) return;
            // 組出訊息內容
            const appointmentSubItemNames = appointmentSubItems
                .map((i) => i.name)
                .join('/');

            const weekday = moment(appointment.date)
                .locale('zh-tw')
                .format('dddd')
                .substring(2);
            const message = [
                '【預約已取消】',
                `很遺憾通知您，以下預約[${appointmentSubItemNames}]服務已取消：`,
                `原預約時間: ${appointment.date} (${weekday}) ${appointment.time}`,
                `原預約門市: ${appointment.store?.name}`,
                `原預約地址: ${appointment.store?.address}`,
                `聯絡電話: ${appointment.store?.phone}`,
                '若您需要重新安排，請隨時透過LINE[預約]或電話聯繫我們。期盼下次有機會為您服務。',
            ].join('\n');

            // 請求 message-integration-gateway 發送 line 通知
            const endpoint = '/clinico/cochlear-implant/line/message';
            const response = await this.axiosInstance.post(endpoint, {
                channelUserId: socialUser.channelUserId,
                messages: [message],
            });
            if (response.status != 200 || response.data?.success != true) {
                throw new httpErrors.BadRequest(
                    `send cochlear implant line message fail: ${response.data}`,
                );
            }
            return;
        } catch (err) {
            const errResp = err?.response?.data?.error?.message ?? err;
            logger.info(`發送電子耳line推播通知失敗，原因：${errResp}`);
            throw errResp;
        }
    }
}
