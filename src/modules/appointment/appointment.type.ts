import Appointment, {
    EnumAppointmentStatus,
    EnumAppointmentIdentity,
    EnumAppointmentTimeType,
    EnumAppointmentMemberType,
    EnumAppointmentDataSource,
    EnumAppointmentCancelReasonType,
    EnumAppointmentServiceStatus,
} from '@clinico/clinico-persistence/dist/models/appointment.model';
import AppointmentItem from '@clinico/clinico-persistence/dist/models/appointmentItem.model';
import {
    EnumAppointmentLogisticsTransferType,
    EnumDeliveryTime,
} from '@clinico/clinico-persistence/dist/models/appointmentLogistics.model';
import { EnumAppointmentPaymentStatus } from '@clinico/clinico-persistence/dist/models/appointmentPaymentRecord.model';
import { EnumAppointmentReferralType } from '@clinico/clinico-persistence/dist/models/appointmentReferral.model';
import AppointmentSubitem from '@clinico/clinico-persistence/dist/models/appointmentSubitem.model';
import { EnumICareTestSocialUsersChannelType } from '@clinico/clinico-persistence/dist/models/iCareTestSocialUsers.model';
import { EnumMemberGender } from '@clinico/clinico-persistence/dist/models/member.model';
import { EnumSocialUserChannelType } from '@clinico/clinico-persistence/dist/models/socialUser.model';
import { Transform } from 'class-transformer';
import {
    IsArray,
    IsDateString,
    IsEmail,
    IsEnum,
    IsMobilePhone,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';
import { JSONSchema } from 'class-validator-jsonschema';
import {
    ObjectType,
    Field,
    Int,
    ArgsType,
    ID,
    registerEnumType,
    Float,
    InputType,
} from 'type-graphql';
import { SearchResultWrap } from '@/common/types/common.type';
import { transformEnum } from '@/common/utils/transformEnum';
import {
    EnumBookingStatus,
    BookingDateFilterInput,
    BookingRentDeviceInput,
} from '../booking/booking.type';
import { EnumCompanyCode } from '../memberAccount/memberAccount.interface';
import { EnumRentDeviceType } from '../rentDevices/rentDevice.type';
import {
    AppointmentItemAndSubitemDTO,
    ClinicoSoundSocialBookingBodyDTO,
    ReqBookingHomeServiceDetectionRSBodyDTO,
} from './appointment.rest.type';

export enum EnumHomeServiceStoreCode {
    CLINICO = 'NH01',
    IB = 'IB-NH01',
    SKD = 'SKD-NH01',
}

export enum EnumAppointmentServiceItem {
    Detection = 'Detection',
    HomeService = 'HomeService',
    HomeServiceDelivery = 'HomeServiceDelivery',
    HomeServiceOverhaul = 'HomeServiceOverhaul',
    HomeServiceResmed = 'HomeServiceResmed',
    HomeServiceResmedPAP = 'HomeServiceResmedPAP',
    HomeServiceResmedReport = 'HomeServiceResmedReport', //RS到府服務－睡眠報告說明
    HomeServiceResmedReturn = 'HomeServiceResmedReturn', //RS到府服務－暫借歸還
    HomeServiceResmedDetection = 'HomeServiceResmedDetection', // RS到府服務-儀器檢測
    Testing = 'Testing',
    ICare = 'ICare',
    Overhaul = 'Overhaul',
    Maintenance = 'Maintenance',
    HA_Delivery = 'HA_Delivery',
    Resmed = 'Resmed',
    ResmedBooking = 'ResmedBooking',
    CI = 'CI',
    Other = 'Other',
    Break = 'Break',
    Return = 'Return',
    ClinicoSoundRepair = 'ClinicoSoundRepair',
    ShoppingCenterResmedBooking = 'ShoppingCenterResmedBooking',
    ShoppingCenterDetection = 'ShoppingCenterDetection',
}

export const EnumAppointmentServiceSubItem = {
    [EnumAppointmentServiceItem.CI]: {
        Consult: '8_1', // 諮詢
        Pickup: '8_2', // 交機
        Cleaning: '8_3', // 清潔除濕
        Supplies: '8_4', // 購買耗材
        Repair: '8_5', // 維修
    },
};

registerEnumType(EnumAppointmentServiceItem, {
    name: 'EnumAppointmentServiceItem',
    description: '預約項目',
    valuesConfig: {
        Detection: { description: '聽力檢測與諮商(1HR)' },
        Testing: { description: '助聽器試聽選配(1.5HR)' },
        Overhaul: { description: '助聽器售後服務(1HR)' },
        Maintenance: { description: '助聽器保養(清潔除濕)：限會員(1小時)' },
        HA_Delivery: { description: '助聽器交貨(1HR)' },
        Resmed: { description: '睡得美相關(1HR)' },
        ResmedBooking: { description: '睡得美預約租借(1HR)' },
        CI: { description: '人工電子耳相關服務(1HR)' },
        ICare: { description: 'iCare 相關' },
        Other: { description: '其他' },
        Break: { description: '外出/門市消毒' },
        Return: { description: '租借歸還(1HR)' },
        HomeService: { description: '到府服務－選配（原系統到府服務）' },
        HomeServiceDelivery: { description: '到府服務－交貨' },
        HomeServiceOverhaul: { description: '到府服務－售後調整' },
        HomeServiceResmed: { description: 'RS到府服務－睡眠檢測' },
        HomeServiceResmedPAP: { description: 'RS到府服務－到府試用正壓呼吸器' },
        HomeServiceResmedReport: { description: 'RS到府服務－睡眠報告說明' },
        HomeServiceResmedReturn: { description: 'RS到府服務－暫借歸還' },
        HomeServiceResmedDetection: { description: 'RS到府服務－儀器檢測' },
        ShoppingCenterResmedBooking: {
            description: '睡眠檢測租借(0.5HR, ShoppingCenter)',
        },
        ShoppingCenterDetection: { description: '聽力檢測與諮商(1HR)' },
    },
});

@ObjectType()
export class AppointmentsWrap extends SearchResultWrap {
    @Field((type) => [Appointment], { nullable: true })
    appointments: Appointment[];
}

@InputType()
export class BookingCreateInput {
    @Field((type) => [BookingRentDeviceInput], {
        nullable: false,
        description: '租借設備',
    })
    bookingRentDevices: BookingRentDeviceInput[];
    @Field((type) => String, { description: '租借日期(起)' })
    startDate: string;
    @Field((type) => String, { description: '租借日期(迄)' })
    endDate: string;
    @Field((type) => ID, {
        nullable: true,
        description: '自選門市編號，未填選預設預約門市',
    })
    storeId?: number;
}

@InputType()
export class AppointmentBookingFilterInput {
    @Field((type) => ID, { nullable: true, description: '門市編號' })
    storeId?: number;
    @Field((type) => String, { nullable: true, description: '借用日(起)' })
    startDate?: string;
    @Field((type) => String, { nullable: true, description: '借用日(迄)' })
    endDate?: string;
    @Field((type) => BookingDateFilterInput, {
        nullable: true,
        description: '查詢涵蓋的預約時間區間',
    })
    coveredDate?: BookingDateFilterInput;
    @Field((type) => EnumBookingStatus, {
        nullable: true,
        description: '預約狀態',
    })
    status?: EnumBookingStatus;
    @Field((type) => ID, { nullable: true, description: '租借設備編號' })
    rentDeviceId?: number;
    @Field((type) => EnumRentDeviceType, {
        nullable: true,
        description: '租借設備種類',
    })
    rentDeviceType?: EnumRentDeviceType;
}

@ArgsType()
export class SearchAppointmentArgs {
    @Field((type) => ID, { nullable: true })
    id?: number;
    @Field((type) => ID, { nullable: true })
    companyId?: number;
    @Field((type) => ID, { nullable: true })
    storeId?: number;
    @Field((type) => ID, { nullable: true })
    memberId?: number;
    @Field({ nullable: true })
    name?: string;
    @Field({ nullable: true })
    mobile?: string;
    @Field((type) => EnumAppointmentMemberType, { nullable: true })
    memberType?: EnumAppointmentMemberType;
    @Field((type) => EnumAppointmentStatus, { nullable: true })
    status?: number;
    @Field({ nullable: true })
    date1?: string;
    @Field({ nullable: true })
    date2?: string;
    @Field((type) => Int, { nullable: true })
    offset?: number;
    @Field((type) => Int, { nullable: true })
    limit?: number;
    @Field((type) => ID, { nullable: true })
    appointmentItemId?: number;
    @Field((type) => EnumAppointmentServiceItem, { nullable: true })
    appointmentItemCode?: EnumAppointmentServiceItem;
    @Field((type) => EnumAppointmentServiceItem, {
        nullable: true,
        description: '要排除的預約類型',
    })
    notAppointmentServiceItemCode?: EnumAppointmentServiceItem;
    @Field((type) => [EnumAppointmentServiceItem], {
        nullable: true,
        description: '預約類型',
    })
    appointmentServiceItemCodes?: EnumAppointmentServiceItem[];
    @Field({ nullable: true })
    memberCode?: string;
    @Field((type) => ID, { nullable: true })
    audiologistId?: number;
    @Field({ nullable: true })
    includeMemberOfHaveNoAudiologist?: boolean;
    @Field((type) => ID, { nullable: true })
    createdUserId?: number;
    @Field({ nullable: true })
    createdUserCode?: string;
    @Field({ nullable: true })
    createdDate1?: string;
    @Field({ nullable: true })
    createdDate2?: string;
    @Field((type) => AppointmentBookingFilterInput, { nullable: true })
    rentAppointmentBookingFilterInput?: AppointmentBookingFilterInput;
    @Field((type) => AppointmentBookingFilterInput, { nullable: true })
    returnAppointmentBookingFilterInput?: AppointmentBookingFilterInput;
    @Field((type) => EnumAppointmentDataSource, {
        nullable: true,
        description: '資料來源',
    })
    dataSource?: EnumAppointmentDataSource;
    @Field((type) => Boolean, {
        nullable: true,
        description: '是否在報到前一天提醒客戶(電話/簡訊)',
    })
    isReminded?: boolean;
    @Field((type) => Boolean, { nullable: true, description: '是否已電話確認' })
    isConfirmed?: boolean;
    @Field((type) => ID, { nullable: true, description: '商機' })
    businessId?: number;
    @Field((type) => ID, { nullable: true, description: '社群人員 id' })
    socialUserId?: number;
    @Field((type) => EnumAppointmentServiceStatus, {
        nullable: true,
        description: '服務狀態',
    })
    serviceStatus?: EnumAppointmentServiceStatus;
}

@ArgsType()
export class SearchAppointmentInternalArgs {
    @Field((type) => ID, { nullable: true })
    id?: number;
    @Field({ nullable: true })
    name?: string;
    @Field({ nullable: true })
    mobile?: string;
    @Field((type) => ID, { nullable: true })
    appointmentItemId?: number;
    @Field((type) => EnumAppointmentServiceItem, { nullable: true })
    appointmentItemCode?: EnumAppointmentServiceItem;
}

@InputType()
export class CreateAppointmentInput {
    @Field((type) => ID)
    storeId: number;
    @Field((type) => EnumAppointmentMemberType)
    memberType: EnumAppointmentMemberType;
    @Field((type) => EnumAppointmentIdentity)
    identity: EnumAppointmentIdentity;
    @Field()
    appointmentDate: string;
    @Field((type) => EnumAppointmentTimeType)
    timeType: EnumAppointmentTimeType;
    @Field()
    time: string;
    @Field((type) => ID, { nullable: true })
    audiologistId?: number;
    @Field((type) => ID, { nullable: true })
    memberId?: number;
    @Field()
    name: string;
    @Field((type) => EnumMemberGender)
    gender: EnumMemberGender;
    @Field({ nullable: true })
    email?: string;
    @Field({ nullable: true })
    birthday?: string;
    @Field()
    mobile: string;
    @Field((type) => [AppointmentItemAndSubitemInput], {
        description: '服務項目與行動方案',
    })
    appointmentItemAndSubitemInput: AppointmentItemAndSubitemInput[];
    @Field({ nullable: true })
    remark?: string;
    @Field((type) => EnumAppointmentDataSource)
    dataSource: EnumAppointmentDataSource;
    @Field((type) => Float, { nullable: true })
    serviceHours?: number;
    @Field({ nullable: true })
    eventCode?: string;
    @Field((type) => BookingCreateInput, { nullable: true })
    rentAppointmentBookingCreateInput?: BookingCreateInput;
    @Field({ nullable: true, description: 'Google Ads 帳號ID' })
    googleAdsCustomerId?: string;
    @Field({ nullable: true, description: 'Google Ads 活動ID' })
    googleAdsCampaignId?: string;
    @Field({ nullable: true, description: 'Google Ads 廣告群組ID' })
    googleAdsAdGroupId?: string;
    @Field({ nullable: true, description: '商機' })
    businessId?: number;
    @Field({ nullable: true, description: '行政區 id' })
    districtId?: number;
    channelUserId?: string;
    /* referral DOCTOR_REFERRAL 從預約異動醫生轉介 */
    @Field((type) => String, {
        nullable: true,
        description: '從預約異動 醫生轉介的轉介代碼',
    })
    doctorReferralCode?: string;
}

export class ReqBookingHomeServiceBody {
    @JSONSchema({
        description: '公司編號',
    })
    @IsEnum(EnumCompanyCode, {
        message: '公司編號',
    })
    @Transform(({ value }) => transformEnum(value, EnumCompanyCode))
    companyCode: EnumCompanyCode;

    /**
     * 預約身分
     */
    @JSONSchema({
        description: '預約身分',
    })
    @IsEnum(EnumAppointmentIdentity, {
        message: '預約身分',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentIdentity))
    identity: EnumAppointmentIdentity;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: new Date(),
    })
    appointmentDate: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
        example: '1100',
    })
    @IsString()
    time: string;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
    })
    @IsString()
    name: string;

    /**
     * 性別
     */
    @JSONSchema({
        description: '性別 Man:1, Female:0',
    })
    @IsEnum(EnumMemberGender, {
        message: '性別',
    })
    @Transform(({ value }) => transformEnum(value, EnumMemberGender))
    gender?: EnumMemberGender;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * 地址
     */
    @IsString()
    @JSONSchema({
        description: '地址',
    })
    address: string;

    /**
     * 到府所在到府所在縣市
     */
    @IsString()
    @JSONSchema({
        description: '到府所在到府所在縣市',
    })
    city: string;

    /**
     * 到府所在鄉鎮市區
     */
    @IsString()
    @JSONSchema({
        description: '到府所在鄉鎮市區',
    })
    area: string;

    /**
     * 是否有聽力檢查報告
     */
    @IsString()
    @JSONSchema({
        description: '是否有聽力檢查報告',
    })
    hasReport: string;

    /**
     * 到府需求原因
     */
    @IsString()
    @JSONSchema({
        description: '到府需求原因',
    })
    reason: string;

    /**
     * 行政區 id
     */
    @IsNumber()
    @JSONSchema({
        description: '行政區 id',
    })
    @IsOptional()
    districtId?: number;

    /**
     * HomeService 到府服務內容
     */
    @IsString({
        each: true,
    })
    @IsArray()
    @JSONSchema({
        description: 'HomeService 到府服務內容',
    })
    appointmentSubItemCodes: string[];

    /**
     * 活動代碼/企業識別碼
     */
    /* @IsString()
    @IsOptional()
    eventCode?: string; */
}

export class ReqBookingStoreRSBody {
    @JSONSchema({
        description: '公司編號',
    })
    @IsEnum(EnumCompanyCode, {
        message: '公司編號',
    })
    @Transform(({ value }) => transformEnum(value, EnumCompanyCode))
    companyCode: EnumCompanyCode;

    @JSONSchema({
        description: '門市代號',
        example: 'N001',
    })
    @IsString()
    storeCode: string;

    @JSONSchema({
        description: '門市ID',
        example: 1,
    })
    @IsNumber()
    storeId: number;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: new Date(),
    })
    appointmentDate: string;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
        example: '1100',
    })
    @IsString()
    time: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsOptional()
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
    })
    @IsString()
    name: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 生日
     */
    @JSONSchema({
        example: '2000-01-01',
    })
    @IsString()
    @IsOptional()
    birthday: string;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * store 預約項目
     */
    @JSONSchema({
        description: 'store 預約項目',
    })
    @IsEnum(EnumAppointmentServiceItem, {
        message: 'store 預約項目',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentServiceItem))
    appointmentItemCode: EnumAppointmentServiceItem;

    /**
     * store 預約子項目
     */
    @JSONSchema({
        description: 'store 預約子項目',
        example: 'HST',
    })
    @IsEnum(EnumRentDeviceType, {
        message: `
      HST:居家睡眠檢測機器
      PAP:試用正壓呼吸器
    `,
    })
    @Transform(({ value }) => transformEnum(value, EnumRentDeviceType))
    appointmentRentDeviceType: EnumRentDeviceType;

    /**
     * 性別
     */
    @JSONSchema({
        description: '性別 Man:1, Female:0',
    })
    @IsOptional()
    @IsEnum(EnumMemberGender, {
        message: '性別',
    })
    @Transform(({ value }) => transformEnum(value, EnumMemberGender))
    gender?: EnumMemberGender;
}

export class ReqBookingHomeServiceRSBody {
    @JSONSchema({
        description: '公司編號',
    })
    @IsEnum(EnumCompanyCode, {
        message: '公司編號',
    })
    @Transform(({ value }) => transformEnum(value, EnumCompanyCode))
    companyCode: EnumCompanyCode;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: new Date(),
    })
    appointmentDate: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
    })
    @JSONSchema({
        example: '1100',
    })
    @IsString()
    time: string;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
    })
    @IsString()
    name: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 生日
     */
    @JSONSchema({
        example: '2000-01-01',
    })
    @IsString()
    @IsOptional()
    birthday: string;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * 地址
     */
    @IsString()
    @JSONSchema({
        description: '地址',
    })
    address: string;

    /**
     * 到府所在到府所在縣市
     */
    @IsString()
    @JSONSchema({
        description: '到府所在到府所在縣市',
    })
    city: string;

    /**
     * 到府所在鄉鎮市區
     */
    @IsString()
    @JSONSchema({
        description: '到府所在鄉鎮市區',
    })
    area: string;

    /**
     * 到府需求原因
     */
    @IsString()
    @JSONSchema({
        description: '到府需求原因',
    })
    reason: string;

    /**
     * homeService 預約項目
     */
    @JSONSchema({
        description: 'homeService 預約項目',
    })
    @IsEnum(EnumAppointmentServiceItem, {
        message: 'homeService 預約項目',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentServiceItem))
    appointmentItemCode: EnumAppointmentServiceItem;

    /**
     * 行政區 id
     */
    @IsNumber()
    @JSONSchema({
        description: '行政區 id',
    })
    @IsOptional()
    districtId?: number;

    /**
     * 交易編號
     */
    @JSONSchema({
        description: '交易編號',
    })
    @IsString()
    @IsOptional()
    tradeCode?: string;

    /**
     * 特店交易編號
     */
    @JSONSchema({
        description: '特店交易編號',
    })
    @IsString()
    @IsOptional()
    merchantTradeCode?: string;

    /**
     * 折扣碼
     */
    @JSONSchema({
        description: '折扣碼',
    })
    @IsString()
    @IsOptional()
    discountCode?: string;

    @JSONSchema({
        description: 'referenceType',
    })
    @IsString()
    @IsOptional()
    referenceType?: string;

    @JSONSchema({
        description: 'referenceCode',
    })
    @IsString()
    @IsOptional()
    referenceCode?: string;

    /**
     * 性別
     */
    @JSONSchema({
        description: '性別 Man:1, Female:0',
    })
    @IsOptional()
    @IsEnum(EnumMemberGender, {
        message: '性別',
    })
    @Transform(({ value }) => transformEnum(value, EnumMemberGender))
    gender?: EnumMemberGender;
}

export class GetAppointmentQuery {
    @JSONSchema({
        description: '預約編號',
    })
    @IsNumber()
    id: number;

    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;
}

export class CancelAppointment {
    @JSONSchema({
        description: '預約編號',
    })
    @IsNumber()
    id: number;
}

export class RefundAppointment {
    /**
     * 交易編號
     */
    @JSONSchema({
        description: '交易編號',
    })
    @IsString()
    tradeCode: string;
}

export class InvoiceAppointment {
    /**
     * 交易編號
     */
    @JSONSchema({
        description: '交易編號',
    })
    @IsString()
    tradeCode: string;

    /**
     * 發票號碼
     */
    @JSONSchema({
        description: '發票號碼',
    })
    @IsString()
    invoice: string;
}

export class UpdateAppointmentRS {
    @JSONSchema({
        description: '預約編號',
    })
    @IsNumber()
    id: number;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: new Date(),
    })
    appointmentDate: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
    })
    @JSONSchema({
        example: '1100',
    })
    @IsString()
    time: string;
}

@InputType()
export class CreateAppointmentInternalInput {
    @Field({ description: '門市代碼' })
    storeCode: string;
    @Field((type) => EnumAppointmentMemberType, {
        nullable: true,
        description: '顧客類別',
    })
    memberType?: EnumAppointmentMemberType;
    @Field((type) => EnumAppointmentIdentity, { description: '預約身分' })
    identity: EnumAppointmentIdentity;
    @Field({ description: '預約日期' })
    appointmentDate: string;
    @Field((type) => EnumAppointmentTimeType)
    timeType: EnumAppointmentTimeType;
    @Field({ description: '預約時間(HHmm)' })
    time: string;
    @Field({ description: '姓名' })
    name: string;
    @Field((type) => EnumMemberGender, { nullable: true, description: '性別' })
    gender?: EnumMemberGender;
    @Field({ nullable: true, description: '信箱' })
    email?: string;
    @Field({ description: '手機' })
    mobile: string;
    @Field({ nullable: true, description: '出生日期' })
    birthday?: string;
    @Field((type) => [AppointmentItemAndSubitemInternalInput], {
        nullable: true,
        description: '服務項目與行動方案',
    })
    appointmentItemAndSubitemInput?: AppointmentItemAndSubitemInternalInput[];
    @Field({ nullable: true, description: '備註' })
    remark?: string;
    @Field((type) => EnumAppointmentDataSource, { description: '預約來源' })
    dataSource: EnumAppointmentDataSource;
    @Field({ nullable: true, description: '活動代碼/企業識別碼' })
    eventCode?: string;
    @Field((type) => BookingCreateInput, { nullable: true })
    rentAppointmentBookingCreateInput?: BookingCreateInput;
    @Field({ nullable: true, description: 'Google Ads 帳號ID' })
    googleAdsCustomerId?: string;
    @Field({ nullable: true, description: 'Google Ads 活動ID' })
    googleAdsCampaignId?: string;
    @Field({ nullable: true, description: 'Google Ads 廣告群組ID' })
    googleAdsAdGroupId?: string;
    @Field({ nullable: true, description: '行政區 id' })
    districtId?: number;
    channelUserId?: string;
    memberId?: number;
}

@InputType()
export class BookingUpdateInput {
    @Field((type) => [BookingRentDeviceInput], {
        nullable: true,
        description: '租借設備',
    })
    bookingRentDevices?: BookingRentDeviceInput[];
    @Field((type) => String, { nullable: true, description: '租借日期(起)' })
    startDate?: string;
    @Field((type) => String, { nullable: true, description: '租借日期(迄)' })
    endDate?: string;
    @Field((type) => EnumBookingStatus, {
        nullable: true,
        description: '預約狀態',
    })
    status?: EnumBookingStatus;
    @Field((type) => ID, {
        nullable: true,
        description: '自選門市編號',
    })
    storeId?: number;
}

@InputType()
export class AppointmentReferralInput {
    @Field((type) => EnumAppointmentReferralType)
    referralType: EnumAppointmentReferralType;

    @Field((type) => String, { nullable: true })
    referralValue?: string;
}

@InputType()
export class AppointmentLogisticsRecipientInfoInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => String, { nullable: true })
    address?: string;

    @Field((type) => String, { nullable: true })
    cellPhone?: string;

    @Field((type) => String, { nullable: true })
    name?: string;

    @Field((type) => String, { nullable: true })
    deliveryDate?: string;

    @Field((type) => EnumDeliveryTime, { nullable: true })
    deliveryTime?: EnumDeliveryTime;

    @Field((type) => String, { nullable: true })
    convenienceStoreId?: string;

    @Field((type) => String, { nullable: true })
    convenienceStoreName?: string;

    @Field((type) => EnumAppointmentLogisticsTransferType, { nullable: true })
    type?: EnumAppointmentLogisticsTransferType;
}
@InputType()
export class UpdateAppointmentInput {
    @Field((type) => ID)
    id: number;
    @Field((type) => EnumAppointmentStatus, { nullable: true })
    status?: EnumAppointmentStatus;
    @Field((type) => EnumAppointmentCancelReasonType, { nullable: true })
    cancelReasonType?: EnumAppointmentCancelReasonType | null;
    @Field((type) => Boolean, {
        nullable: true,
        description: '是否在報到前一天提醒客戶(電話/簡訊)',
    })
    isReminded?: boolean;
    @Field((type) => Boolean, { nullable: true, description: '是否已電話確認' })
    isConfirmed?: boolean;
    @Field((type) => Date, { nullable: true, description: '確認時間' })
    confirmedAt?: Date;
    @Field((type) => ID, { nullable: true })
    storeId?: number;
    @Field((type) => EnumAppointmentMemberType, { nullable: true })
    memberType?: EnumAppointmentMemberType;
    @Field((type) => EnumAppointmentIdentity, { nullable: true })
    identity?: EnumAppointmentIdentity;
    @Field({ nullable: true })
    appointmentDate?: string;
    @Field((type) => EnumAppointmentTimeType, { nullable: true })
    timeType?: EnumAppointmentTimeType;
    @Field({ nullable: true })
    time?: string;
    @Field((type) => ID, { nullable: true })
    audiologistId?: number;
    @Field((type) => ID, { nullable: true })
    memberId?: number;
    @Field({ nullable: true })
    name?: string;
    @Field((type) => EnumMemberGender, { nullable: true })
    gender?: EnumMemberGender;
    @Field({ nullable: true })
    email?: string;
    @Field({ nullable: true })
    mobile?: string;
    @Field((type) => [AppointmentItemAndSubitemInput], {
        nullable: true,
        description: '服務項目與行動方案',
    })
    appointmentItemAndSubitemInput?: AppointmentItemAndSubitemInput[];
    @Field({ nullable: true })
    remark?: string;
    @Field((type) => EnumAppointmentDataSource, { nullable: true })
    dataSource?: EnumAppointmentDataSource;
    @Field((type) => Float, { nullable: true })
    serviceHours?: number;
    @Field({ nullable: true })
    eventCode?: string;
    @Field((type) => BookingUpdateInput, { nullable: true })
    rentAppointmentBookingUpdateInput?: BookingUpdateInput;
    @Field((type) => BookingUpdateInput, { nullable: true })
    returnAppointmentBookingUpdateInput?: BookingUpdateInput;
    @Field((type) => ID, { nullable: true })
    businessId?: number;
    @Field((type) => EnumAppointmentServiceStatus, { nullable: true })
    serviceStatus?: EnumAppointmentServiceStatus;

    /* --- 直接更新 appointmant 關聯的 business 資訊 --- */
    @Field((type) => ID, {
        nullable: true,
        description: '直接更新關聯的 businessId 的 usingOpportunityId',
    })
    usingOpportunityId?: number;
    @Field((type) => ID, {
        nullable: true,
        description: '直接更新關聯的 businessId 的 dealOpportunityId',
    })
    dealOpportunityId?: number;
    @Field((type) => String, { nullable: true, description: '預計結束日即' })
    expectedClosedDate?: string;

    /* referral DOCTOR_REFERRAL 從預約異動醫生轉介 */
    @Field((type) => String, {
        nullable: true,
        description: '從預約異動 醫生轉介的轉介代碼',
    })
    doctorReferralCode?: string;

    /* appointment_logistics_recipient_info 更新收件人資訊*/
    @Field((type) => AppointmentLogisticsRecipientInfoInput, { nullable: true })
    logisticsRecipientInfoInput?: AppointmentLogisticsRecipientInfoInput;

    /* appointment_referral 更新後續導流資訊*/
    @Field((type) => AppointmentReferralInput, { nullable: true })
    appointmentReferralInput?: AppointmentReferralInput;
}

@InputType()
export class UpdateAppointmentInternalInput {
    @Field((type) => ID)
    id: number;
    @Field({ description: '門市代碼' })
    storeCode: string;
    @Field((type) => EnumAppointmentMemberType, {
        nullable: true,
        description: '顧客類別',
    })
    memberType?: EnumAppointmentMemberType;
    @Field((type) => EnumAppointmentIdentity, { description: '預約身分' })
    identity: EnumAppointmentIdentity;
    @Field({ description: '預約日期' })
    appointmentDate: string;
    @Field((type) => EnumAppointmentTimeType)
    timeType: EnumAppointmentTimeType;
    @Field({ description: '預約時間(HHmm)' })
    time: string;
    @Field({ description: '姓名' })
    name: string;
    @Field((type) => EnumMemberGender, { nullable: true, description: '性別' })
    gender?: EnumMemberGender;
    @Field({ nullable: true, description: '信箱' })
    email?: string;
    @Field({ description: '手機' })
    mobile: string;
    @Field({ nullable: true, description: '出生日期' })
    birthday?: string;
    @Field((type) => [AppointmentItemAndSubitemInternalInput], {
        nullable: true,
        description: '服務項目與行動方案',
    })
    appointmentItemAndSubitemInput?: AppointmentItemAndSubitemInternalInput[];
    @Field({ nullable: true, description: '備註' })
    remark?: string;
    @Field((type) => EnumAppointmentDataSource, { description: '預約來源' })
    dataSource: EnumAppointmentDataSource;
    @Field({ nullable: true, description: '活動代碼/企業識別碼' })
    eventCode?: string;
    @Field((type) => BookingUpdateInput, { nullable: true })
    rentAppointmentBookingUpdateInput?: BookingUpdateInput;
}

@InputType()
export class CancelAppointmentInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => EnumAppointmentCancelReasonType, { nullable: true })
    cancelReasonType: EnumAppointmentCancelReasonType;
}

@ObjectType()
export class AppointmentItemAndSubitem {
    @Field((type) => AppointmentItem)
    appointmentItem: AppointmentItem;
    @Field((type) => [AppointmentSubitem], { nullable: true })
    appointmentSubItems: AppointmentSubitem[];
}

@InputType()
export class AppointmentItemAndSubitemInput {
    @Field((type) => ID, { description: '服務項目' })
    appointmentItemId: number;
    @Field((type) => [ID], { nullable: true, description: '服務項目-行動方案' })
    appointmentSubItemIds?: number[];
}

@InputType()
export class AppointmentItemAndSubitemInternalInput {
    @Field((type) => EnumAppointmentServiceItem, { description: '預約項目' })
    appointmentItemCode: EnumAppointmentServiceItem;
    @Field((type) => [String], {
        nullable: true,
        description: '服務項目-行動方案',
    })
    appointmentSubItemCodes?: string[];
}

export enum EnumAppointmenSubscriptiontType {
    Created = 'Created',
    Deleted = 'Deleted',
}

registerEnumType(EnumAppointmenSubscriptiontType, {
    name: 'EnumAppointmenSubscriptiontType',
});

@ObjectType()
export class AppointmentSubscription {
    @Field((type) => EnumAppointmenSubscriptiontType)
    type: EnumAppointmenSubscriptiontType;

    @Field((type) => Appointment)
    appointment: Appointment;
}

@ArgsType()
export class AppointmentSubscriptionArgs {
    @Field((type) => ID, { nullable: true })
    storeId?: number;
    @Field((type) => ID, { nullable: true })
    audiologistId?: number;
    @Field({ nullable: true })
    date1?: string;
    @Field({ nullable: true })
    date2?: string;
}

export type FindLatestAppointmentParams = {
    companyId: number;
    channelUserId: string;
};

export type CreateAppointmentBySocialParams = {
    /**
     * line Id
     */
    channelUserId: string;
    /**
     * 社群類型 ex: LINE, FB...
     */
    channelType: EnumSocialUserChannelType;
    storeCode: string;
    /**
     * 預約日期
     */
    appointmentDate: string;
    /**
     * 預約時間類型
     */
    timeType: EnumAppointmentTimeType;
    /**
     * 預約時間(HHmm)
     */
    time: string;
    /**
     * 姓名
     */
    name: string;
    /**
     * 信箱
     */
    email?: string;
    /**
     * 手機
     */
    mobile: string;
    /**
     * 備註
     */
    remark?: string;
    /**
     * 行政區 id
     */
    districtId?: number;
    /**
     * 服務內容
     */
    appointmentItemAndSubitems: AppointmentItemAndSubitemDTO[];
};

export type CreateAppointmentByShoppingCenterParams = Omit<
    CreateAppointmentBySocialParams,
    'channelUserId' | 'channelType'
>;

export type FindShoppingCenterAppointmentParams = {
    storeId: number;
    appointmentDate: string;
    time: string;
    timeType: EnumAppointmentTimeType;
};

export class ShoppingCenterAppointmentReqDTO {
    storeCode: string;
    serviceItemCode: EnumAppointmentServiceItem;
}
export type CreateAppointmentPaymentRecordParams = {
    appointmentId: number;
    tradeType?: string;
    tradeCode?: string;
    merchantTradeCode?: string;
    discountCode?: string;
    referenceType?: string;
    referenceCode?: string;
};

export type UpdateAppointmentPaymentRecordParams = {
    appointmentId: number;
    invoice?: string;
    status?:
        | EnumAppointmentPaymentStatus.Refund
        | EnumAppointmentPaymentStatus.Refunded;
};

export enum EnumAppointmentServiceItemSendEmail {
    HomeServiceResmed = 'HomeServiceResmed',
    HomeServiceResmedPAP = 'HomeServiceResmedPAP',
    HomeServiceResmedDetection = 'HomeServiceResmedDetection',
}

export type CreateAppointmentForClinicoSoundParams =
    ClinicoSoundSocialBookingBodyDTO;

export type ReqBookingHomeServiceDetectionRSParams =
    ReqBookingHomeServiceDetectionRSBodyDTO;

// 預約單關聯社群用戶類別(科林/iCare)
export enum EnumAppointmentSocialUserType {
    clinico,
    iCare,
}
registerEnumType(EnumAppointmentSocialUserType, {
    name: 'EnumAppointmentSocialUserType',
    description: '預約單關聯社群用戶類別',
    valuesConfig: {
        clinico: { description: '科林' },
        iCare: { description: 'iCare' },
    },
});

// 整合不同社群用戶類別的頻道類型
export const EnumAppointmentSocialUserChannelType = {
    ...EnumSocialUserChannelType,
    ...EnumICareTestSocialUsersChannelType,
} as const; // readonly
registerEnumType(EnumAppointmentSocialUserChannelType, {
    name: 'EnumAppointmentSocialUserChannelType',
    description: '預約單關聯社群頻道類型',
});

// 預約單關聯社群用戶(科林/iCare)
@ObjectType()
export class AppointmentSocialUser {
    @Field((type) => EnumAppointmentSocialUserType, {
        description: '社群用戶類別',
    })
    type: EnumAppointmentSocialUserType;

    @Field((type) => EnumAppointmentSocialUserChannelType, {
        description: '社群頻道類型',
    })
    channelType: typeof EnumAppointmentSocialUserChannelType;

    @Field((type) => String, { description: '社群用戶id' })
    channelUserId: string;

    @Field((type) => String, { description: '社群用戶暱稱' })
    channelUserName: string;
}
