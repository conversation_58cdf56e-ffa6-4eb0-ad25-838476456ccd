import { EnumAppointmentTimeType } from '@clinico/clinico-persistence/dist/models/appointment.model';
import {
    EnumAppointmentLogisticsTransferType,
    EnumDeliveryTime,
} from '@clinico/clinico-persistence/dist/models/appointmentLogistics.model';
import { EnumCochlearImplantSocialUsersChannelType } from '@clinico/clinico-persistence/dist/models/cochlearImplantSocialUsers.model';
import { EnumMemberGender } from '@clinico/clinico-persistence/dist/models/member.model';
import { EnumSocialUserChannelType } from '@clinico/clinico-persistence/dist/models/socialUser.model';
import { EnumStoreType } from '@clinico/clinico-persistence/dist/models/store.model';
import { Transform, Type } from 'class-transformer';
import {
    IsEnum,
    IsDateString,
    IsString,
    IsEmail,
    IsOptional,
    IsMobilePhone,
    IsNumber,
    IsArray,
    IsInt,
    ValidateNested,
    IsBoolean,
} from 'class-validator';
import { JSONSchema } from 'class-validator-jsonschema';
import * as moment from 'moment';
import { transformEnum } from '@/common/utils/transformEnum';
import { EnumCompanyCode } from '../memberAccount/memberAccount.interface';
import { EnumAppointmentServiceItem } from './appointment.type';

export class ReqServiceItemSearchBodyDTO {
    @JSONSchema({
        description: '服務項目代碼',
    })
    @IsOptional()
    @IsString({ each: true })
    codes: string[];
}

export class AppointmentStoreDTO {
    @IsInt()
    @IsOptional()
    id: number;

    @IsString()
    @IsOptional()
    name: string;

    @IsString()
    @IsOptional()
    code: string;

    @IsString()
    @IsOptional()
    address: string;

    @IsString()
    @IsOptional()
    phone: string;
}

export class AppointmentDTO {
    @IsInt()
    @IsOptional()
    id: number;

    @IsInt()
    @IsOptional()
    memberId?: number;

    @IsString()
    @IsOptional()
    channelUserId?: string;

    @IsString()
    @IsOptional()
    mobile?: string;

    @ValidateNested()
    @Type(() => AppointmentStoreDTO)
    store?: AppointmentStoreDTO | null;

    @IsString()
    @IsOptional()
    date?: string;

    @IsString()
    @IsOptional()
    time?: string;

    @IsString()
    @IsOptional()
    endTime?: string;

    @ValidateNested({ each: true })
    @Type(() => AppointmentItemDTO)
    @IsOptional()
    appointmentItemAndSubitems?: AppointmentItemDTO[];
}

export class AppointmentItemDTO {
    @IsNumber()
    id: number;

    @IsString()
    code: string;

    @IsString()
    name: string;

    @JSONSchema({
        description: '服務時間',
    })
    @IsInt()
    serviceHours: number;

    @IsEnum(EnumStoreType)
    @IsOptional()
    storeType?: EnumStoreType | null;

    @IsNumber()
    viewOrder: number;

    @ValidateNested({ each: true })
    @Type(() => AppointmentSubItemDTO)
    appointmentSubItems: AppointmentSubItemDTO[];
}

export class AppointmentSubItemDTO {
    @IsNumber()
    id: number;

    @IsString()
    code: string;

    @IsString()
    name: string;

    @IsNumber()
    viewOrder: number;
}

export class SocialBookingBodyDTO {
    @JSONSchema({
        description: '門市代碼',
        example: 'N881',
    })
    @IsString()
    storeCode: string;

    @JSONSchema({
        description: '社群類型',
        example: EnumSocialUserChannelType.LINE,
    })
    @IsEnum(EnumSocialUserChannelType)
    channelType: EnumSocialUserChannelType;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: '2024-07-30',
    })
    appointmentDate: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
        example: '1100',
    })
    @IsString()
    time: string;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
        example: 'appointmentTest',
    })
    @IsString()
    name: string;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * line Id
     */
    @IsString()
    @JSONSchema({
        description: 'line Id',
        example: 'testlineId',
    })
    channelUserId: string;

    /**
     * 行政區 id
     */
    @IsNumber()
    @JSONSchema({
        description: '行政區 id',
        example: 1,
    })
    @IsOptional()
    districtId?: number;
}

export class RepairReason {
    @IsArray()
    @IsNumber({}, { each: true })
    ids: number[];

    @IsString()
    @IsOptional()
    otherReason?: string;
}

export class ClinicoSoundSocialBookingBodyDTO extends SocialBookingBodyDTO {
    /**
     * SN
     */
    @IsString()
    @JSONSchema({
        description: 'SN',
        example: 'testSN',
    })
    SN: string;

    /**
     * 料號CODE
     */
    @IsString()
    @JSONSchema({
        description: '料號CODE',
        example: 'materialCode',
    })
    materialCode: string;

    /**
     * 維修原因
     */
    @ValidateNested()
    @Type(() => RepairReason)
    @JSONSchema({
        description: '維修原因',
    })
    repairReason: RepairReason;
}

export class BookingBodyDTO
    implements Omit<SocialBookingBodyDTO, 'channelUserId' | 'channelType'>
{
    name: string;
    mobile: string;
    time: string;
    storeCode: string;
    appointmentDate: string;
    timeType: EnumAppointmentTimeType;
    email?: string | undefined;
    remark?: string | undefined;
    districtId?: number | undefined;
}

export class ShoppingCenterBookingBodyDTO extends BookingBodyDTO {
    @JSONSchema({
        description: '預約項目代號',
    })
    serviceItems: AppointmentItemAndSubitemDTO[];
}

export class ReqSocialBookingBodyDTO extends SocialBookingBodyDTO {
    /**
     * 服務內容
     */
    @IsArray()
    @JSONSchema({
        description: '預約服務內容',
    })
    @ValidateNested({ each: true })
    @Type(() => AppointmentItemAndSubitemDTO)
    appointmentItemAndSubitems: AppointmentItemAndSubitemDTO[];
}

export class AppointmentItemAndSubitemDTO {
    @IsEnum(EnumAppointmentServiceItem)
    @IsString()
    appointmentItemCode: EnumAppointmentServiceItem;

    @JSONSchema({
        description: '預約服務子項目',
        example: ['2_1', '2_2'],
    })
    @IsString({
        each: true,
    })
    @IsOptional()
    appointmentSubItemCodes?: string[];
}

export class ReceiptInfo {
    @IsString()
    @IsOptional()
    @JSONSchema({
        description: '收件超商代碼',
        example: '12345',
    })
    convenienceStoreId?: string;

    @IsString()
    @IsOptional()
    @JSONSchema({
        description: '收件超商名稱',
        example: '12345',
    })
    convenienceStoreName?: string;

    @IsString()
    @JSONSchema({
        description: '收件人手機',
        example: '0966000000',
    })
    mobile: string;

    @IsString()
    @JSONSchema({
        description: '收件人姓名',
    })
    name: string;

    @IsString()
    @IsOptional()
    @JSONSchema({
        description: '收件人/超商地址',
        example: '測試地址',
    })
    address?: string;
}

export class SenderInfo {
    @IsString()
    @JSONSchema({
        description: '寄件人姓名',
        example: 'testName',
    })
    name: string;

    @IsString()
    @JSONSchema({
        description: '寄件人電話, 寄件人電話、手機擇一必填',
        example: '0233330000',
    })
    phone: string;

    @IsString()
    @JSONSchema({
        description: '寄件人手機, 寄件人電話、手機擇一必填',
        example: '0966000000',
    })
    mobile: string;

    @IsString()
    @JSONSchema({
        description: '寄件人地址',
        example: '測試地址',
    })
    address: string;
}

export class LogisticsInfo {
    @IsEnum(EnumAppointmentLogisticsTransferType)
    @JSONSchema({
        description: '宅配類型',
    })
    @Transform(({ value }) =>
        transformEnum(value, EnumAppointmentLogisticsTransferType),
    )
    type: EnumAppointmentLogisticsTransferType;

    @ValidateNested()
    @Type(() => ReceiptInfo)
    recipient: ReceiptInfo;

    @IsString()
    @IsOptional()
    @JSONSchema({
        description: '希望配達日期',
        example: moment().format('YYYY-MM-DD'),
    })
    deliveryDate?: string;

    @IsEnum(EnumDeliveryTime, {
        message: '希望配達時間',
    })
    @IsOptional()
    @Transform(({ value }) => transformEnum(value, EnumDeliveryTime))
    @JSONSchema({
        description: '希望配達時間',
    })
    deliveryTime?: EnumDeliveryTime;
}

export class ReqBookingHomeServiceDetectionRSBodyDTO {
    @JSONSchema({
        description: '公司編號',
    })
    @IsEnum(EnumCompanyCode, {
        message: '公司編號',
    })
    @Transform(({ value }) => transformEnum(value, EnumCompanyCode))
    companyCode: EnumCompanyCode;

    @JSONSchema({
        description: '門市代號',
        example: 'NH02',
    })
    @IsString()
    storeCode: string;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
    })
    @IsString()
    name: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 生日
     */
    @JSONSchema({
        example: '2000-01-01',
    })
    @IsString()
    @IsOptional()
    birthday: string;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 折扣碼
     */
    @JSONSchema({
        description: '折扣碼',
    })
    @IsString()
    @IsOptional()
    discountCode?: string;

    /**
     * 身高
     */
    @JSONSchema({
        description: '身高',
    })
    @IsNumber()
    height: number;

    /**
     * 體重
     */
    @JSONSchema({
        description: '體重',
    })
    @IsNumber()
    weight: number;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * 預約項目
     */
    @JSONSchema({
        description: '預約項目',
    })
    @IsEnum(EnumAppointmentServiceItem, {
        message: '預約項目',
    })
    appointmentItemCode: EnumAppointmentServiceItem;

    /**
     * 交易編號
     */
    @JSONSchema({
        description: '交易編號',
    })
    @IsString()
    @IsOptional()
    tradeCode?: string;

    @JSONSchema({
        description: 'referenceType',
    })
    @IsString()
    @IsOptional()
    referenceType?: string;

    @JSONSchema({
        description: 'referenceCode',
    })
    @IsString()
    @IsOptional()
    referenceCode?: string;

    /**
     * 性別
     */
    @JSONSchema({
        description: '性別 Man:1, Female:0',
    })
    @IsOptional()
    @IsEnum(EnumMemberGender, {
        message: '性別',
    })
    @Transform(({ value }) => transformEnum(value, EnumMemberGender))
    gender?: EnumMemberGender;

    /**
     * 宅配資訊
     */
    @ValidateNested()
    @Type(() => LogisticsInfo)
    @JSONSchema({
        description: '宅配資訊',
    })
    logistics: LogisticsInfo;
}

export class CochlearImplantAppointmentItemAndSubitem extends AppointmentItemAndSubitemDTO {
    @IsBoolean()
    @IsOptional()
    @JSONSchema({
        description: '取貨交機損壞證明',
    })
    damageCertificate?: boolean;

    @IsBoolean()
    @IsOptional()
    @JSONSchema({
        description: '取貨交機保固證明書',
    })
    accessoryWarrantyDoc?: boolean;
}

export class ReqSocialBookingCochlearImplantBodyDTO {
    @JSONSchema({
        description: '門市代碼',
        example: 'N881',
    })
    @IsString()
    storeCode: string;

    /**
     * 預約日期
     */
    @IsDateString()
    @JSONSchema({
        description: '預約日期',
        example: '2024-07-30',
    })
    appointmentDate: string;

    /**
     * 預約時間類型
     */
    @JSONSchema({
        description: '預約時間類型',
    })
    @IsEnum(EnumAppointmentTimeType, {
        message: '預約時間類型',
    })
    @Transform(({ value }) => transformEnum(value, EnumAppointmentTimeType))
    timeType: EnumAppointmentTimeType;

    /**
     * 預約時間(HHmm)
     */
    @JSONSchema({
        description: '預約時間(HHmm)',
        example: '1100',
    })
    @IsString()
    time: string;

    /**
     * 姓名
     */
    @JSONSchema({
        description: '姓名',
        example: 'appointmentTest',
    })
    @IsString()
    name: string;

    /**
     * 信箱
     */
    @JSONSchema({
        description: '信箱',
    })
    @IsString()
    @IsEmail()
    @IsOptional()
    email?: string;

    /**
     * 手機
     */
    @IsMobilePhone('zh-TW')
    @JSONSchema({
        example: '0966000000',
    })
    mobile: string;

    /**
     * 備註
     */
    @JSONSchema({
        description: '備註',
    })
    @IsString()
    @IsOptional()
    remark?: string;

    /**
     * 行政區 id
     */
    @IsNumber()
    @JSONSchema({
        description: '行政區 id',
        example: 1,
    })
    @IsOptional()
    districtId?: number;

    /**
     * 服務內容
     */
    @IsArray()
    @JSONSchema({
        description: '預約服務內容',
    })
    @ValidateNested({ each: true })
    @Type(() => CochlearImplantAppointmentItemAndSubitem)
    appointmentItemAndSubitems: CochlearImplantAppointmentItemAndSubitem[];

    @IsString()
    @JSONSchema({
        description: '社群用戶id',
    })
    channelUserId: string;

    @IsEnum(EnumCochlearImplantSocialUsersChannelType)
    @JSONSchema({
        description: '社群平台類型',
        example: 'LINE',
    })
    channelType: EnumCochlearImplantSocialUsersChannelType;
}
