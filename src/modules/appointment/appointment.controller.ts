import { Helpers } from '@clinico/clinico-node-framework';
import {
    Body,
    JsonController,
    Get,
    HeaderParam,
    NotFoundError,
    Post,
    UseBefore,
    Param,
    QueryParam,
} from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import Container, { Inject, Service } from 'typedi';
import { RequiredAccessKeyAuthenticated } from '@/common/middlewares/auth/auth.koa.middleware';
import logger from '@/modules/logger/logger.service';
import { AppointmentLogisticsRepository } from '../appointmentLogistics/repositories/appointmentLogistics.repo';
import { AppointmentLogisticsService } from '../appointmentLogistics/services/appointmentLogistics.service';
import { AppointmentLogisticsUseCase } from '../appointmentLogistics/usecases/appointmentLogistics.usecase';
import { AppointmentLogisticsRecipientInfoRepository } from '../appointmentLogisticsRecipientInfo/repositories/appointmentLogisticsRecipientInfo.repo';
import { AppointmentLogisticsRecipientInfoUseCase } from '../appointmentLogisticsRecipientInfo/usecases/appointmentLogisticsRecipientInfo.usecase';
import { BookingSMSService } from '../booking/bookingSMS.service';
import { AppointmentItemService } from '../item/appointmentItem.service';
import { UserService } from '../user/user.service';
import {
    ReqSocialBookingBodyDTO,
    ReqServiceItemSearchBodyDTO,
    AppointmentItemDTO,
    AppointmentSubItemDTO,
    ShoppingCenterBookingBodyDTO,
    ClinicoSoundSocialBookingBodyDTO,
    ReqBookingHomeServiceDetectionRSBodyDTO,
    ReqSocialBookingCochlearImplantBodyDTO,
} from './appointment.rest.type';
import {
    CancelAppointment,
    EnumAppointmentServiceItem,
    GetAppointmentQuery,
    InvoiceAppointment,
    RefundAppointment,
    ReqBookingHomeServiceBody,
    ReqBookingHomeServiceRSBody,
    ReqBookingStoreRSBody,
    UpdateAppointmentRS,
} from './appointment.type';
import { AppointmentClinicoSoundService } from './services/appointment.clinicoSound.service';
import { AppointmentHsService } from './services/appointment.hs.service';
import { AppointmentService } from './services/appointment.service';
import { AppointmentSocialService } from './services/appointment.social.service';
import { AppointmentStoreService } from './services/appointment.store.service';

@Service()
@JsonController('/appointments')
@UseBefore(RequiredAccessKeyAuthenticated())
export class AppointmentController {
    constructor(
        @Inject()
        private appointmentHsService: AppointmentHsService,
        @Inject()
        private appointmentStoreService: AppointmentStoreService,
        @Inject()
        private bookingSMSService: BookingSMSService,
        @Inject()
        private appointmentService: AppointmentService,
        @Inject()
        private userService: UserService,
        @Inject()
        private appointmentItemSvc: AppointmentItemService,
        @Inject()
        private appointmentSocialSvc: AppointmentSocialService,
        @Inject()
        private appointmentClinicoSoundSvc: AppointmentClinicoSoundService,
        @Inject()
        private appointmentLogisticsRecipientInfoUseCase: AppointmentLogisticsRecipientInfoUseCase,
        @Inject()
        private appointmentLogisticsUseCase: AppointmentLogisticsUseCase,
        @Inject()
        private appointmentLogisticsService: AppointmentLogisticsService,
        @Inject()
        private appointmentLogisticsRecipientInfoRepo: AppointmentLogisticsRecipientInfoRepository,
        @Inject()
        private appointmentLogisticsRepo: AppointmentLogisticsRepository,
    ) {
        if (!this.appointmentHsService) {
            this.appointmentHsService = Container.get(AppointmentHsService);
        }
        if (!this.appointmentStoreService) {
            this.appointmentStoreService = Container.get(
                AppointmentStoreService,
            );
        }
        if (!this.bookingSMSService) {
            this.bookingSMSService = Container.get(BookingSMSService);
        }
        if (!this.appointmentService) {
            this.appointmentService = Container.get(AppointmentService);
        }
        if (!this.userService) {
            this.userService = Container.get(UserService);
        }
        if (!this.appointmentItemSvc) {
            this.appointmentItemSvc = Container.get(AppointmentItemService);
        }
        if (!this.appointmentSocialSvc) {
            this.appointmentSocialSvc = Container.get(AppointmentSocialService);
        }
        if (!this.appointmentClinicoSoundSvc) {
            this.appointmentClinicoSoundSvc = Container.get(
                AppointmentClinicoSoundService,
            );
        }
        if (!this.appointmentLogisticsRecipientInfoUseCase) {
            this.appointmentLogisticsRecipientInfoUseCase = Container.get(
                AppointmentLogisticsRecipientInfoUseCase,
            );
        }
        if (!this.appointmentLogisticsUseCase) {
            this.appointmentLogisticsUseCase = Container.get(
                AppointmentLogisticsUseCase,
            );
        }
        if (!this.appointmentLogisticsService) {
            this.appointmentLogisticsService = Container.get(
                AppointmentLogisticsService,
            );
        }
        if (!this.appointmentLogisticsRecipientInfoRepo) {
            this.appointmentLogisticsRecipientInfoRepo = Container.get(
                AppointmentLogisticsRecipientInfoRepository,
            );
        }
        if (!this.appointmentLogisticsRepo) {
            this.appointmentLogisticsRepo = Container.get(
                AppointmentLogisticsRepository,
            );
        }
    }

    @Post('/social/booking')
    @OpenAPI({
        summary: '社群軟體線上預約功能',
    })
    async bookingBySocial(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqSocialBookingBodyDTO,
    ) {
        const appointment =
            await this.appointmentSocialSvc.createAppointmentBySocial(body);
        const data = appointment.toJSON();
        return data;
    }

    @Post('/items/search')
    @OpenAPI({
        summary: '查詢服務項目',
    })
    @ResponseSchema(AppointmentItemDTO, { isArray: true })
    async searchServiceItems(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqServiceItemSearchBodyDTO,
    ): Promise<AppointmentItemDTO[]> {
        const { rows } = await this.appointmentItemSvc.search({
            ...body,
            regionId: 1,
        });
        const data = rows.map((i) => {
            const appointmentSubItems: AppointmentSubItemDTO[] =
                i.appointmentSubitems.map((sub) => ({
                    id: sub.id,
                    code: sub.code,
                    name: sub.name,
                    viewOrder: sub.viewOrder,
                }));
            const item: AppointmentItemDTO = {
                id: i.id,
                code: i.code,
                name: i.name,
                serviceHours: i.serviceHours,
                storeType: i.storeType,
                viewOrder: i.viewOrder,
                appointmentSubItems,
            };
            return item;
        });
        return data;
    }

    @Post('/homeService/booking')
    async bookingHomeService(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqBookingHomeServiceBody,
    ) {
        const appointment = await this.appointmentHsService.bookingHomeService(
            body,
        );
        const data = appointment.toJSON();
        this.bookingSMSService.sendHomeServiceSMSForBooking(appointment);
        return data;
    }

    @Get('/homeService/subItems')
    async getHomeServiceSubItems(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
    ) {
        const items = await this.appointmentHsService.getHomeServiceSubItems();
        if (items.length == 0) {
            throw new NotFoundError('not found HomeService sub Items');
        }
        const data = items.map((v) => v.toJSON());
        return data;
    }

    @Post('/store/booking/rs')
    async bookingStoreRS(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqBookingStoreRSBody,
    ) {
        try {
            const appointment =
                await this.appointmentStoreService.bookingStoreRS(body);
            return Helpers.Json.success(appointment.toJSON());
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/homeService/booking/rs')
    async bookingHomeServiceRS(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqBookingHomeServiceRSBody,
    ) {
        try {
            const appointment =
                await this.appointmentHsService.bookingHomeServiceRS(body);
            this.bookingSMSService.sendHomeServiceSMSForBookingRS(
                appointment,
                body.appointmentItemCode,
            );

            return Helpers.Json.success(appointment.toJSON());
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    /**
     * 到府服務-儀器檢測
     */
    @Post('/homeService/detection/booking/rs')
    async bookingHomeServiceDetectionRS(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqBookingHomeServiceDetectionRSBodyDTO,
    ) {
        try {
            const appointment =
                await this.appointmentHsService.bookingHomeServiceDetectionRS(
                    body,
                );
            this.bookingSMSService.sendHomeServiceSMSForBookingRS(
                appointment,
                body.appointmentItemCode,
            );
            // 建立宅配收件者資料
            const logistics = body.logistics;
            await this.appointmentLogisticsRecipientInfoUseCase.create({
                appointmentId: appointment.id,
                ...logistics,
            });

            return Helpers.Json.success(appointment.toJSON());
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/queryOne')
    async queryOneAppointment(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: GetAppointmentQuery,
    ) {
        try {
            const data = await this.appointmentService.findOneByIdAndMobile(
                body.id,
                body.mobile,
            );
            if (!data) {
                return Helpers.Json.success({});
            }
            const logisticsRecipientInfo =
                await this.appointmentLogisticsRecipientInfoRepo.findOneByAppointmentId(
                    data.id,
                );
            const logistics = await this.appointmentLogisticsRepo.search({
                appointmentId: data.id,
                deleted: false,
            });
            return Helpers.Json.success({
                ...data?.toJSON(),
                logisticsRecipientInfo: logisticsRecipientInfo?.toJSON(),
                logistics: logistics.rows.map((v) => v.toJSON()),
            });
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/rs/cancel')
    async cancelAppointment(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: CancelAppointment,
    ) {
        try {
            // 檢查預約物流是否已出貨
            await this.appointmentLogisticsUseCase.checkAppointmentHasLogistics(
                body.id,
            );

            await this.appointmentService.cancelInternal({
                id: body.id,
                regionId: 1,
            });
            return Helpers.Json.success({});
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/rs/refund')
    async refundAppointment(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: RefundAppointment,
    ) {
        try {
            await this.appointmentService.refundInternal({
                tradeCode: body.tradeCode,
            });
            return Helpers.Json.success({});
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/rs/invoice')
    async invoiceAppointment(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: InvoiceAppointment,
    ) {
        try {
            await this.appointmentService.invoiceInternal({
                tradeCode: body.tradeCode,
                invoice: body.invoice,
            });
            return Helpers.Json.success({});
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/rs/update')
    async updateAppointment(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: UpdateAppointmentRS,
    ) {
        try {
            const user = await this.userService.findOneOrErrorByCode('IT001');
            const data =
                await this.appointmentService.updateAppointmentAndItems({
                    ...body,
                    regionId: 1,
                    updatedUserId: user.id,
                });

            return Helpers.Json.success(data.appointment.toJSON());
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/social/clinicoSound/booking')
    @OpenAPI({
        summary: '社群軟體線上預約功能(ClinicoSound)',
    })
    async bookingBySocialForClinicoSound(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ClinicoSoundSocialBookingBodyDTO,
    ) {
        const appointment =
            await this.appointmentClinicoSoundSvc.createAppointment(body);
        const data = appointment.toJSON();
        return data;
    }

    @Post('/shoppingCenter/booking')
    @OpenAPI({
        summary: '線上預約功能(ShoppingCenter)',
    })
    async bookingForShoppingCenter(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ShoppingCenterBookingBodyDTO,
    ) {
        try {
            const { serviceItems: appointmentItemAndSubitems, ...rest } = body;
            const appointment =
                await this.appointmentSocialSvc.createAppointmentForShoppingCenter(
                    {
                        appointmentItemAndSubitems,
                        ...rest,
                    },
                );

            return Helpers.Json.success(appointment.toJSON());
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Get('/shoppingCenter/today/booking/:storeCode/search')
    @OpenAPI({
        summary: '拿取今天預約(ShoppingCenter)',
    })
    async shoppingCenterBookingSearch(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Param('storeCode') storeCode: string,
        @QueryParam('serviceItemCode')
        serviceItemCode: EnumAppointmentServiceItem,
    ) {
        try {
            const params = {
                storeCode,
                serviceItemCode,
            };
            const appointment =
                await this.appointmentService.getTodayAppointmentsByStoreAndCalculateServiceItemTime(
                    params,
                );
            return Helpers.Json.success(appointment);
        } catch (err) {
            return Helpers.Json.error(err);
        }
    }

    @Post('/cochlear-implant/social/booking')
    @OpenAPI({
        summary: '電子耳社群線上預約功能',
    })
    async socialBookingICare(
        @HeaderParam('access-key') key: string,
        @HeaderParam('access-secret') secret: string,
        @Body() body: ReqSocialBookingCochlearImplantBodyDTO,
    ) {
        try {
            const result =
                await this.appointmentSocialSvc.createAppointmentForCochlearImplant(
                    body,
                );
            return Helpers.Json.success(result);
        } catch (err) {
            logger.info(`建立電子耳預約失敗：${err}`);
            throw err;
        }
    }
}
