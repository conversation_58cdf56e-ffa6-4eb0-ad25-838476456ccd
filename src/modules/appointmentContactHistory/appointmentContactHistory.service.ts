import { Helpers } from '@clinico/clinico-node-framework';
import db from '@clinico/clinico-persistence';
import AppointmentContactHistory from '@clinico/clinico-persistence/dist/models/appointmentContactHistory.model';
import * as DataLoader from 'dataloader';
import * as httpErrors from 'http-errors';
import { groupBy } from 'lodash';
import { ISearchResult } from '@/common/interfaces/common.interface';
import { BaseService } from '@/common/providers/base.service';
import {
    ICreateAppointmentContactHistory,
    ISearchAppointmentContactHistoryParams,
    IUpdateAppointmentContactHistory,
} from './appointmentContactHistory.interface';

export class AppointmentContactHistoryService extends BaseService<AppointmentContactHistory> {
    async findOneOrError(id: number): Promise<AppointmentContactHistory> {
        const appointmentContactHistory = await this.findOne(id);
        if (!appointmentContactHistory) {
            throw new httpErrors.NotFound(
                'Appointment connection history not found',
            );
        }
        return appointmentContactHistory;
    }

    async search(
        params: ISearchAppointmentContactHistoryParams,
    ): Promise<ISearchResult<AppointmentContactHistory[]>> {
        const filters: any = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.appointmentId) {
            filters.appointmentId = params.appointmentId;
        }
        const result = await db.AppointmentContactHistory.findAndCountAll({
            include: [
                { model: db.User, as: 'createdUser' },
                { model: db.User, as: 'updatedUser' },
            ],
            where: filters,
            order: [['id', 'DESC']],
            offset: params.offset,
            limit: params.limit,
        });
        return result;
    }

    async create(
        params: ICreateAppointmentContactHistory,
    ): Promise<AppointmentContactHistory> {
        const date = params.appointmentDate;
        const time = params.appointmentTime ?? '00:00';
        const newAppointmentContactHistory = db.AppointmentContactHistory.build(
            {
                appointmentId: params.appointmentId,
                date: Helpers.Date.parse(`${date} ${time}`, 'YYYY-MM-DD HH:mm'),
                createdUserId: params.createUserId,
                remark: params.remark,
            },
        );
        await this.validate(newAppointmentContactHistory);

        const tx = await db.sequelize.transaction();
        try {
            await newAppointmentContactHistory.save({ transaction: tx });
            await tx.commit();
            const appointmentContactHistory = await this.findOneOrError(
                newAppointmentContactHistory.id,
            );
            return appointmentContactHistory;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    async update(
        params: IUpdateAppointmentContactHistory,
    ): Promise<AppointmentContactHistory> {
        const contactHistory = await this.findOneOrError(params.id);
        const date = params.appointmentDate;
        const time = params.appointmentTime ?? '00:00';
        if (params.appointmentDate) {
            contactHistory.date = Helpers.Date.parse(
                `${date} ${time}`,
                'YYYY-MM-DD HH:mm',
            ).toDate();
        }
        contactHistory.remark = <any>(params.remark ? params.remark : null);

        const tx = await db.sequelize.transaction();
        try {
            await contactHistory.save({ transaction: tx });
            await tx.commit();
            const updatedContactHistory = await this.findOneOrError(
                contactHistory.id,
            );
            return updatedContactHistory;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    async validate(appointmentContactHistory: AppointmentContactHistory) {
        if (!appointmentContactHistory.appointmentId) {
            throw new httpErrors.BadRequest('Appointment id is required');
        }
        if (!appointmentContactHistory.date) {
            throw new httpErrors.BadRequest('Date is required');
        }
        if (!Helpers.Date.isDate(appointmentContactHistory.date)) {
            throw new httpErrors.BadRequest('Incorrect date format');
        }
    }

    // Dataloader for finding contact histories by appointment ID
    private findByAppointmentIdDataloader = new DataLoader(
        async (appointmentIds: readonly number[]) => {
            const res = await db.AppointmentContactHistory.findAll({
                include: [
                    { model: db.User, as: 'createdUser' },
                    { model: db.User, as: 'updatedUser' },
                ],
                where: {
                    appointmentId: { [db.Op.in]: appointmentIds as number[] },
                },
                order: [['id', 'DESC']],
            });
            const grouped = groupBy(res, 'appointmentId');
            return appointmentIds.map((key) => grouped[key] || []);
        },
        {
            cache: false,
            batchScheduleFn: (callback) => setTimeout(callback, 10),
        },
    );

    async searchByAppointmentIdUseDataloader(
        appointmentId: number,
    ): Promise<AppointmentContactHistory[]> {
        return await this.findByAppointmentIdDataloader.load(appointmentId);
    }
}
