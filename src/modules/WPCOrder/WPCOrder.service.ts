import { Helpers } from '@clinico/clinico-node-framework';
import db from '@clinico/clinico-persistence';
import * as httpErrors from 'http-errors';
import * as moment from 'moment';
import { Inject, Service } from 'typedi';
import TipTopDb from '@/common/databases/tiptop.database';
import { ISearchResult } from '@/common/interfaces/common.interface';
import { CompanyService } from '../company/company.service';
import { MemberService } from '../member/member.service';
import { allowAuditOrderTypes } from '../orderAudit/orderAudit.config';
import { UserService } from '../user/user.service';
import {
    EnumWPCOrderProcess,
    IVStoreOrders,
    IWPCOrderSearchParams,
    SearchSimpleOrderParams,
    WPCBackOrder,
    WPCOrder,
    WPCShoplineOrder,
} from './WPCOrder.interface';
import { EnumWPCOrderType } from './WPCOrder.type';

@Service()
export class WPCOrderService {
    @Inject()
    private memberService: MemberService;
    @Inject()
    private companyService: CompanyService;
    @Inject()
    private userService: UserService;

    async findOneOrErrorByOrderCodeAndCompanyId(
        orderCode: string,
        companyId: number,
    ): Promise<WPCOrder> {
        const WPCOrder = await this.search({ orderCode, companyId });
        if (WPCOrder.count === 0) {
            throw new httpErrors.NotFound('WPCOrder not found');
        } else if (WPCOrder.count > 1) {
            throw new httpErrors.NotFound('WPCOrder redundancy');
        }
        return WPCOrder.rows[0];
    }

    async findOneSimple(
        params: IWPCOrderSearchParams,
    ): Promise<WPCOrder | null> {
        const result = await this.searchSimple(params);
        return result.count === 1 ? result.rows[0] : null;
    }

    async search(
        params: IWPCOrderSearchParams,
    ): Promise<ISearchResult<WPCOrder[]>> {
        const filters: string[] = [];

        let sql = `
            SELECT * FROM (
                SELECT 
                    V_STORE_ORDERS.* 
                    -- 是否可以新增稽核單
                    ,CASE WHEN ORDER_AUDIT.ORDER_CODE IS NOT NULL THEN 'Y' ELSE 'N' END AS IS_AUDIT_CREATABLE
                    -- 是否可以稽核
                    ,CASE 
                        WHEN V_STORE_ORDERS.ORDER_TYPE IN ('AS301', 'AS324') AND ORDER_AUDIT.ORDER_CODE IS NOT NULL AND ORDER_AUDIT.BACK_ORDER_QTY = 0 THEN 'Y'
                        WHEN V_STORE_ORDERS.ORDER_TYPE =   'ASH01'           AND ORDER_AUDIT.ORDER_CODE IS NOT NULL AND ORDER_AUDIT.SHIPPED_QTY > 0    THEN 'Y'
                        WHEN V_STORE_ORDERS.ORDER_TYPE =   'AS319'           AND ORDER_AUDIT.ORDER_CODE IS NOT NULL THEN 'Y'
                        ELSE 'N' 
                    END AS IS_AUDITABLE
                    -- 稽核查詢出貨用的日期
                    ,ORDER_AUDIT.SHIPPING_DATE AS AUDITABLE_START_DATE
                    ,ORDER_AUDIT.TRACKING_NUMBER AS TRACKING_NUMBER
                FROM V_STORE_ORDERS
                LEFT JOIN (
                    SELECT 
                        od.ORDER_CODE 	  		AS ORDER_CODE
                        ,od.COMPANY 	  		AS COMPANY
                        ,MAX(od.SHIPPED_QTY)    AS SHIPPED_QTY		-- 已出貨（最大值）
                        ,MAX(od.BACK_ORDER_QTY)	AS BACK_ORDER_QTY 	-- 未出貨（最大值）
                        ,MAX(so.SHIPPING_DATE)  AS SHIPPING_DATE    -- 出貨時間（最近一筆）
                        ,so.tracking_number AS TRACKING_NUMBER -- 物流單號
                    FROM      V_STORE_ORDER_DETAILS           od
                    LEFT JOIN V_STORE_ORDERS		           o ON o.ORDER_CODE = od.ORDER_CODE AND o.COMPANY = od.COMPANY
                    LEFT JOIN V_STORE_MATERIALS	               m ON m.CODE = od.MATERIAL_CODE
                    LEFT JOIN V_STORE_SHIPPING_ORDER_DETAILS sod ON sod.ORDER_CODE = od.ORDER_CODE AND sod.COMPANY = od.COMPANY AND sod.ORDER_SEQ_ID = od.SEQ_ID 
                    LEFT JOIN V_STORE_SHIPPING_ORDERS         so ON so.SHIPPING_CODE = sod.SHIPPING_CODE  
                    WHERE m.MATERIAL_TYPE IN ('A')
                    AND   o.ORDER_TYPE    IN (${Helpers.Str.toSQLInString(
                        allowAuditOrderTypes,
                    )})
                    GROUP BY od.ORDER_CODE, od.COMPANY, so.TRACKING_NUMBER
                ) ORDER_AUDIT ON ORDER_AUDIT.ORDER_CODE = V_STORE_ORDERS.ORDER_CODE AND ORDER_AUDIT.COMPANY = V_STORE_ORDERS.COMPANY
            )
            WHERE 1 = 1
        `;

        /** 由於Order Type 包含大量未知Type，目前先把Type匡在已知範圍內 */
        const orderTypes = Helpers.Str.toSQLInString(
            params.orderTypes || Object.values(EnumWPCOrderType),
        );
        filters.push(`"ORDER_TYPE" IN (${orderTypes})`);

        if (params.orderCode) {
            filters.push(`"ORDER_CODE" = '${params.orderCode}'`);
        }
        if (params.orderCodes) {
            filters.push(
                `"ORDER_CODE" IN ('${params.orderCodes.join("','")}')`,
            );
        }
        if (params.memberCode) {
            filters.push(`"SHIPPING_CUSTOMER_CODE" = '${params.memberCode}'`);
        }
        if (params.memberId) {
            const member = await this.memberService.findOneOrError(
                params.memberId,
            );
            filters.push(`"SHIPPING_CUSTOMER_CODE" = '${member.code}'`);
        }
        if (params.companyId) {
            const company = await this.companyService.findOneOrError(
                params.companyId,
            );
            filters.push(`"COMPANY" = '${company.code}'`);
        }
        if (params.companyCode) {
            filters.push(`"COMPANY" = '${params.companyCode}'`);
        }
        if (params.storeIds && params.storeIds.length > 0) {
            const stores = await db.Store.findAll({
                where: { id: params.storeIds },
            });
            const storeCodes = stores.map((row) => row.code);
            filters.push(
                `"SHIPPING_DEPT_CODE" IN (${Helpers.Str.toSQLInString(
                    storeCodes,
                )})`,
            );
        }
        if (params.userId) {
            const user = await this.userService.findOneOrError(params.userId);
            filters.push(`"USER_CODE" = '${user.code}'`);
        }
        if (params.createdDate1) {
            filters.push(
                `ORDER_DATE >= to_date('${params.createdDate1}', 'yyyy-mm-dd')`,
            );
        }
        if (params.createdDate2) {
            filters.push(
                `ORDER_DATE <= to_date('${params.createdDate2}', 'yyyy-mm-dd')`,
            );
        }
        if (params.isAuditSubmitable != undefined) {
            const isAuditSubmittable = params.isAuditSubmitable ? `'Y'` : `'N'`;
            filters.push(`"IS_AUDITABLE" = ${isAuditSubmittable}`);
        }
        if (params.auditableStartDate1) {
            filters.push(
                `AUDITABLE_START_DATE >= to_date('${params.auditableStartDate1}', 'yyyy-mm-dd')`,
            );
        }
        if (params.auditableStartDate2) {
            filters.push(
                `AUDITABLE_START_DATE <= to_date('${params.auditableStartDate2}', 'yyyy-mm-dd')`,
            );
        }
        if (params.trackingNumber) {
            filters.push(`"TRACKING_NUMBER" = '${params.trackingNumber}'`);
        }

        /** Where */
        if (filters.length > 0) {
            sql += `AND ${filters.join(' AND ')}`;
        }

        /** Order By */
        sql += ` ORDER BY "ORDER_DATE" DESC `;

        /** count */
        const countSql = `SELECT COUNT(*) AS "count" FROM (${sql})`;
        const countResult = await TipTopDb.raw(countSql);
        const count = countResult[0].count;

        /** result */
        let resultSql = sql;
        if (params.limit) {
            resultSql = `SELECT * FROM ( ${sql} ) WHERE rownum <= ${params.limit} `;
        }
        if (params.offset) {
            resultSql = `SELECT * FROM ( ${sql} ) WHERE rownum > ${params.offset} `;
        }
        if (params.offset && params.limit) {
            resultSql = `
                SELECT * FROM (
                    SELECT row_.*, ROWNUM rownum_ FROM (
                        ${sql}
                    ) row_ WHERE rownum <= ${params.offset + params.limit}) 
                WHERE rownum_ > ${params.offset}
            `;
        }

        const result = await TipTopDb.raw<IVStoreOrders[]>(resultSql);
        const orders = result.map((row) => {
            const wpcOrder: WPCOrder = {
                code: row.ORDER_CODE,
                orderType: <EnumWPCOrderType>row.ORDER_TYPE,
                companyCode: row.COMPANY,
                departmentCode: row.SHIPPING_DEPT_CODE,
                managerCode: row.USER_CODE,
                memberCode: row.SHIPPING_CUSTOMER_CODE,
                process: <EnumWPCOrderProcess>row.PROCESS,
                createdDate: moment(row.ORDER_DATE).format('YYYY-MM-DD'),
                isAuditSubmitable: Helpers.Str.isTrue(row.IS_AUDITABLE),
                isAuditable: Helpers.Str.isTrue(row.IS_AUDIT_CREATABLE),
                auditableStartDate: row.AUDITABLE_START_DATE
                    ? moment(row.AUDITABLE_START_DATE).format('YYYY-MM-DD')
                    : undefined,
                amount: Helpers.Str.toIntOrZero(row.TOTAL_AMOUNT),
                trackingNumber: row.TRACKING_NUMBER ?? undefined,
            };
            return wpcOrder;
        });
        return { rows: orders, count: count };
    }

    /** 沒有稽核相關邏輯的查詢 */
    async searchSimple(
        params: SearchSimpleOrderParams,
    ): Promise<ISearchResult<WPCOrder[]>> {
        const sql = TipTopDb('V_STORE_ORDERS').withSchema('DBUSER1');

        sql.whereIn(
            'ORDER_TYPE',
            params.orderTypes || Object.values(EnumWPCOrderType),
        );
        if (params.orderCode) {
            sql.andWhere({ ORDER_CODE: params.orderCode });
        }
        if (params.memberId) {
            const member = await this.memberService.findOneOrError(
                params.memberId,
            );
            sql.andWhere({ SHIPPING_CUSTOMER_CODE: member.code });
        }
        if (params.companyId) {
            const company = await this.companyService.findOneOrError(
                params.companyId,
            );
            sql.andWhere({ COMPANY: company.code });
        }
        if (params.companyCode) {
            sql.andWhere({ COMPANY: params.companyCode });
        }
        if (params.storeIds && params.storeIds.length > 0) {
            const stores = await db.Store.findAll({
                where: { id: params.storeIds },
            });
            sql.whereIn(
                'SHIPPING_DEPT_CODE',
                stores.map((row) => row.code),
            );
        }
        if (params.userId) {
            const user = await this.userService.findOneOrError(params.userId);
            sql.andWhere({ USER_CODE: user.code });
        }
        if (params.createdDate1) {
            sql.andWhereRaw(`ORDER_DATE >= to_date(?, 'yyyy-mm-dd')`, [
                params.createdDate1,
            ]);
        }
        if (params.createdDate2) {
            sql.andWhereRaw(`ORDER_DATE <= to_date(?, 'yyyy-mm-dd')`, [
                params.createdDate2,
            ]);
        }

        const countSql = sql.clone();
        const countRaw = countSql.count('* as count').toQuery();
        const countResult = await TipTopDb.raw(countRaw);
        const count = Helpers.Str.toIntOrZero(countResult[0].count);

        if (params.limit) {
            sql.limit(params.limit);
        }
        if (params.offset) {
            sql.offset(params.offset);
        }

        const orderRaw = sql.select().orderBy('ORDER_DATE', 'desc').toQuery();
        const orderResult = await TipTopDb.raw<IVStoreOrders[]>(orderRaw);
        const orders: WPCOrder[] = orderResult.map((row) => {
            return {
                code: row.ORDER_CODE,
                orderType: <EnumWPCOrderType>row.ORDER_TYPE,
                companyCode: row.COMPANY,
                departmentCode: row.SHIPPING_DEPT_CODE,
                managerCode: row.USER_CODE,
                memberCode: row.SHIPPING_CUSTOMER_CODE,
                process: <EnumWPCOrderProcess>row.PROCESS,
                createdDate: moment(row.ORDER_DATE).format('YYYY-MM-DD'),
                amount: Helpers.Str.toIntOrZero(row.TOTAL_AMOUNT),
            };
        });

        return { rows: orders, count: count };
    }

    async searchForShopline(params: SearchSimpleOrderParams) {
        const sql = TipTopDb('V_STORE_SHOPLINE_DETAILS').withSchema('DBUSER1');

        if (params.orderCode) {
            sql.andWhere({ ORDER_CODE: params.orderCode });
        }
        if (params.memberId) {
            const member = await this.memberService.findOneOrError(
                params.memberId,
            );
            sql.andWhere({ SHIPPING_CUSTOMER_CODE: member.code });
        }
        if (params.companyId) {
            const company = await this.companyService.findOneOrError(
                params.companyId,
            );
            sql.andWhere({ COMPANY: company.code });
        }
        if (params.companyCode) {
            sql.andWhere({ COMPANY: params.companyCode });
        }
        if (params.storeIds && params.storeIds.length > 0) {
            const stores = await db.Store.findAll({
                where: { id: params.storeIds },
            });
            sql.whereIn(
                'SHIPPING_DEPT_CODE',
                stores.map((row) => row.code),
            );
        }
        if (params.userId) {
            const user = await this.userService.findOneOrError(params.userId);
            sql.andWhere({ USER_CODE: user.code });
        }
        if (params.createdDate1) {
            sql.andWhereRaw(`ORDER_DATE >= to_date(?, 'yyyy-mm-dd')`, [
                params.createdDate1,
            ]);
        }
        if (params.createdDate2) {
            sql.andWhereRaw(`ORDER_DATE <= to_date(?, 'yyyy-mm-dd')`, [
                params.createdDate2,
            ]);
        }
        if (params.isPost != undefined) {
            sql.andWhereRaw(`POST = ?`, [params.isPost ? 'Y' : 'N']);
        }
        if (params.shoplineOrderCode) {
            sql.andWhere({ PARENT_ORDER_CODE: params.shoplineOrderCode });
        }

        const countSql = sql.clone();
        const countRaw = countSql.count('* as count').toQuery();
        const countResult = await TipTopDb.raw(countRaw);
        const count = Helpers.Str.toIntOrZero(countResult[0].count);

        if (params.limit) {
            sql.limit(params.limit);
        }
        if (params.offset) {
            sql.offset(params.offset);
        }

        const orderRaw = sql.select().orderBy('ORDER_DATE', 'desc').toQuery();
        const orderResult = await TipTopDb.raw(orderRaw);

        const orders: WPCShoplineOrder[] = orderResult.map((row) => {
            return {
                code: row.ORDER_CODE,
                orderType: <EnumWPCOrderType>row.ORDER_TYPE,
                companyCode: row.COMPANY,
                departmentCode: row.SHIPPING_DEPT_CODE,
                managerCode: row.USER_CODE,
                memberCode: row.SHIPPING_CUSTOMER_CODE,
                process: <EnumWPCOrderProcess>row.PROCESS,
                createdDate: moment(row.ORDER_DATE).format('YYYY-MM-DD'),
                amount: Helpers.Str.toIntOrZero(row.TOTAL_AMOUNT),
                shoplineOrderCode: row.PARENT_ORDER_CODE,
                isPost: Helpers.Str.isTrue(row.POST),
            };
        });

        return { rows: orders, count: count };
    }

    async findBackOrdersWrapByMemberCode(
        params: IWPCOrderSearchParams,
        memberCode: string,
    ): Promise<ISearchResult<WPCBackOrder[]>> {
        const sql = `select t.*
                     from (select so.MEMBER_CODE
                                , SUM(sod.QTY)            as QTY
                                , SUM(sod.BACK_ORDER_QTY) as BACK_ORDER_QTY
                                , sm.NAME
                                , sm.CODE
                           from V_STORE_ORDERS so
                                    left join V_STORE_ORDER_DETAILS sod
                                              on so.ORDER_CODE = sod.ORDER_CODE and so.COMPANY = sod.COMPANY
                                    left join V_STORE_MATERIALS sm on sod.MATERIAL_CODE = sm.CODE
                           where so.STATUS = 'Y'
                             and sm.MATERIAL_TYPE = '4'
                             and sod.BACK_ORDER_QTY > 0
                             and so.ORDER_TYPE in
                                 ('AS301', 'AS309', 'AS324', 'ASH01', 'AS319', 'ASF01', 'AS326', 'AS312', 'AS313')
                             and so.SHIPPING_CUSTOMER_CODE = '${memberCode}'
                             and so.process != '2'
                             and sod.IS_CLOSE = 'N'
                           group by so.MEMBER_CODE
                                  , sm.NAME
                                  , sm.CODE) t
                     where 1 = 1`;

        /** count */
        const countSql = `SELECT COUNT(*) AS "count" FROM (${sql})`;
        const countResult = await TipTopDb.raw(countSql);
        const count = countResult[0].count;

        let resultSql = sql;
        if (params.limit) {
            resultSql = `SELECT * FROM ( ${sql} ) WHERE rownum <= ${params.limit} `;
        }
        if (params.offset) {
            resultSql = `SELECT * FROM ( ${sql} ) WHERE rownum > ${params.offset} `;
        }
        if (params.offset && params.limit) {
            resultSql = `
                SELECT * FROM (
                    SELECT row_.*, ROWNUM rownum_ FROM (
                        ${sql}
                    ) row_ WHERE rownum <= ${params.offset + params.limit}) 
                WHERE rownum_ > ${params.offset}
            `;
        }

        const orderResult = await TipTopDb.raw(resultSql);
        const backOrders: WPCBackOrder[] = orderResult.map((row: any) => {
            return {
                memberCode: row.MEMBER_CODE,
                qty: row.QTY,
                backOrderQty: row.BACK_ORDER_QTY,
                materialCode: row.CODE,
                materialName: row.NAME,
            };
        });

        return { rows: backOrders, count: count };
    }
}
