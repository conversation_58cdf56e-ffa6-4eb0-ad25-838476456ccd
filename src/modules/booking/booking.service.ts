import { Helpers } from '@clinico/clinico-node-framework';
import db from '@clinico/clinico-persistence';
import { EnumRegion } from '@clinico/clinico-persistence/dist/base.enum';
import * as httpErrors from 'http-errors';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Transaction } from 'sequelize/types';
import { Service, Inject } from 'typedi';
import { ISearchResult } from '@/common/interfaces/common.interface';
import { BaseService } from '@/common/providers/base.service';
import { DateCondition } from '../../common/types/common.type';
import { AppointmentLibService } from '../appointment/services/appointment.lib.service';
import { BookingDeviceService } from '../booking/bookingDevice.service';
import { BookingDevice } from '../booking/bookingDevice.type';
import { CustomClosingTimeService } from '../customClosingTime/customClosingTime.service';
import { RentDeviceService } from '../rentDevices/rentDevice.service';
import {
    IBookingSearchParams,
    ICreateBookingInput,
    IUpdateBookingInput,
    IDeleteBooking,
    IBookingNextBusinessDayParams,
    IRentDeviceDailyInfoParams,
    IDateBookingRentDevice,
    IBookingsByDateRangeInput,
    ICheckSetDevice,
    IUpdateBookingDevicesAtCreateLogistic,
} from './booking.interface';
import {
    Booking,
    EnumBookingStatus,
    EnumRentDeviceType,
    EnumRentDeviceStatus,
    NextBusinessDay,
    RentDeviceDailyInfo,
    BookingRentDevice,
} from './booking.type';

@Service()
export class BookingService extends BaseService<Booking> {
    @Inject()
    private customClosingTimeService: CustomClosingTimeService;
    @Inject()
    private appointmentLibService: AppointmentLibService;
    @Inject()
    private bookingDeviceService: BookingDeviceService;
    @Inject()
    private rentDeviceService: RentDeviceService;

    async findOneOrError(id: number): Promise<Booking> {
        const booking = await this.findOne(id);
        if (!booking) throw new httpErrors.NotFound('booking not found');
        return booking;
    }

    async findOneOrErrorWithTransaction(
        id: number,
        tx: Transaction,
    ): Promise<Booking> {
        const bookings = await this.search({ id: id }, tx);
        if (!bookings) throw new httpErrors.NotFound('booking not found');
        return bookings.rows[0];
    }

    async findByRentDeviceId(
        rentDeviceId: number,
    ): Promise<ISearchResult<Booking[]>> {
        const data = await db.Booking.findAndCountAll({
            distinct: true,
            include: [
                {
                    model: db.BookingDevice,
                    include: [{ model: db.RentDevice }],
                    where: { rentDeviceId: rentDeviceId },
                },
            ],
        });
        return data;
    }

    async findOneByRentAppointmentId(id: number): Promise<Booking | null> {
        const bookings = await this.search({
            rentAppointmentId: id,
        });
        if (bookings.count == 0) {
            return null;
        }
        return bookings.rows[0];
    }

    async search(
        params: IBookingSearchParams,
        tx?: Transaction,
    ): Promise<ISearchResult<Booking[]>> {
        let filters: any = {};
        const storeFilters: any = {};
        const bookingDeviceFilters: any = {};

        filters.deleted = false;

        if (params.companyId) {
            storeFilters.companyId = params.companyId;
        }
        if (params.id) {
            filters.id = params.id;
        }
        if (params.rentAppointmentId) {
            filters.rentAppointmentId = params.rentAppointmentId;
        }

        if (params.returnAppointmentId) {
            filters.returnAppointmentId = params.returnAppointmentId;
        }
        if (params.appointmentId) {
            filters = {
                ...filters,
                [db.Op.or]: [
                    { rentAppointmentId: params.appointmentId },
                    { returnAppointmentId: params.appointmentId },
                ],
            };
        }

        if (params.storeIds) {
            if (!_.isArray(params.storeIds)) {
                throw new httpErrors.BadRequest('storeIds is not valid');
            }
            filters.storeId = { [db.Op.in]: params.storeIds };
        }
        if (params.status != undefined) {
            filters.status = params.status;
        }
        if (params.memberId) {
            filters.memberId = params.memberId;
        }

        if (params.rentDeviceIds) {
            if (!_.isArray(params.rentDeviceIds)) {
                throw new httpErrors.BadRequest('rentDeviceIds is not valid');
            }
            bookingDeviceFilters.rentDeviceId = {
                [db.Op.in]: params.rentDeviceIds,
            };
        }

        if (params.rentDeviceTypes) {
            if (!_.isArray(params.rentDeviceTypes)) {
                throw new httpErrors.BadRequest('rentDeviceTypes is not valid');
            }
            bookingDeviceFilters.rentDeviceType = {
                [db.Op.in]: params.rentDeviceTypes,
            };
        }

        if (params.zoneId) {
            storeFilters.zoneId = params.zoneId;
        }
        if (params.zone2Id) {
            storeFilters.zone2Id = params.zone2Id;
        }
        if (params.storeId) {
            storeFilters.id = params.storeId;
        }

        if (params.startDate) {
            if (!filters.startDate) {
                filters.startDate = {};
            }
            filters.startDate[db.Op.and] = [
                { [db.Op.eq]: Helpers.Date.parse(params.startDate).toDate() },
            ];
        }
        if (params.endDate) {
            if (!filters.endDate) {
                filters.endDate = {};
            }
            filters.endDate[db.Op.and] = [
                { [db.Op.eq]: Helpers.Date.parse(params.endDate).toDate() },
            ];
        }
        if (params.startDateCondition) {
            if (!filters.startDate) {
                filters.startDate = {};
            }
            filters.startDate[db.Op.and] = this.generateDateFilter(
                params.startDateCondition,
            );
        }
        if (params.endDateCondition) {
            if (!filters.endDate) {
                filters.endDate = {};
            }
            filters.endDate[db.Op.and] = this.generateDateFilter(
                params.endDateCondition,
            );
        }
        if (params.coveredDate?.startDate) {
            if (!Helpers.Date.isDate(params.coveredDate.startDate)) {
                throw new httpErrors.BadRequest(
                    'booking startDate is not valid',
                );
            }
            if (!filters.endDate) {
                filters.endDate = {};
            }
            filters.endDate[db.Op.and] = [
                ...(filters.endDate[db.Op.and] || []),
                {
                    [db.Op.gte]: Helpers.Date.parse(
                        params.coveredDate.startDate,
                    ).toDate(),
                },
            ];
        }
        if (params.coveredDate?.endDate) {
            if (!Helpers.Date.isDate(params.coveredDate.endDate)) {
                throw new httpErrors.BadRequest('booking endDate is not valid');
            }
            if (!filters.startDate) {
                filters.startDate = {};
            }
            filters.startDate[db.Op.and] = [
                ...(filters.startDate[db.Op.and] || []),
                {
                    [db.Op.lte]: Helpers.Date.parse(
                        params.coveredDate.endDate,
                    ).toDate(),
                },
            ];
        }

        if (params.greaterThanEqualDate1) {
            if (!Helpers.Date.isDate(params.greaterThanEqualDate1)) {
                throw new httpErrors.BadRequest(
                    'greaterThanEqualDate1 is not valid',
                );
            }
            filters.startDate = {
                [db.Op.gte]: Helpers.Date.parse(
                    params.greaterThanEqualDate1,
                ).toDate(),
            };
        }
        if (params.greaterThanEqualDate2) {
            if (!Helpers.Date.isDate(params.greaterThanEqualDate2)) {
                throw new httpErrors.BadRequest(
                    'greaterThanEqualDate2 is not valid',
                );
            }
            filters.endDate = {
                [db.Op.lte]: Helpers.Date.parse(
                    params.greaterThanEqualDate2,
                ).toDate(),
            };
        }
        if (params.lessThanEqualDate1) {
            if (!Helpers.Date.isDate(params.lessThanEqualDate1)) {
                throw new httpErrors.BadRequest(
                    'lessThanEqualDate1 is not valid',
                );
            }
            filters.startDate = {
                [db.Op.lte]: Helpers.Date.parse(
                    params.lessThanEqualDate1,
                ).toDate(),
            };
        }
        if (params.lessThanEqualDate2) {
            if (!Helpers.Date.isDate(params.lessThanEqualDate2)) {
                throw new httpErrors.BadRequest(
                    'lessThanEqualDate2 is not valid',
                );
            }
            filters.endDate = {
                [db.Op.lte]: Helpers.Date.parse(
                    params.lessThanEqualDate2,
                ).toDate(),
            };
        }

        if (params.receiveDate) {
            // 取貨日
            if (!Helpers.Date.isDate(params.receiveDate)) {
                throw new httpErrors.BadRequest('receiveDate is not valid');
            }
            filters.startDate = {
                [db.Op.eq]: Helpers.Date.parse(params.receiveDate).toDate(),
            };
        }

        if (params.isCanceled) {
            // 是否取消
            filters.status = {
                [db.Op.eq]: EnumBookingStatus.Canceled,
            };
        }

        const result = await db.Booking.findAndCountAll({
            distinct: true,
            include: [
                {
                    model: db.Store,
                    include: [{ model: db.Company }],
                    where: storeFilters,
                    required: true,
                    as: 'store',
                },
                { model: db.Member },
                {
                    model: db.BookingDevice,
                    where: bookingDeviceFilters,
                    include: [
                        {
                            model: db.RentDevice,
                        },
                    ],
                },
                { model: db.User, as: 'createdUser' },
                { model: db.User, as: 'updatedUser' },
                { model: db.SleepTest },
                { model: db.Appointment, as: 'rentAppointment' },
                { model: db.Appointment, as: 'returnAppointment' },
            ],
            where: filters,
            order: [['start_date', 'DESC']],
            limit: params.limit,
            offset: params.offset,
            transaction: tx,
        });
        return result;
    }

    async create(
        params: ICreateBookingInput,
        options?: { tx: Transaction },
    ): Promise<Booking> {
        const tx = options?.tx || (await db.sequelize.transaction());

        try {
            const createBooking = await db.Booking.build({
                storeId: params.storeId,
                memberId: params.memberId,
                rentAppointmentId: params.rentAppointmentId,
                startDate: params.startDate,
                endDate: params.endDate,
                status: EnumBookingStatus.UnReceive,
                createdUserId: params.createdUserId,
            });

            let booking = await createBooking.save({ transaction: tx });

            if (!_.isArray(params.bookingRentDevices)) {
                throw new httpErrors.BadRequest(
                    'bookingRentDevices is not valid',
                );
            }
            await this.bookingDeviceService.bulkCreate(
                {
                    bookingId: booking.id,
                    bookingRentDevices: params.bookingRentDevices,
                },
                tx,
            );

            const newBooking = await this.findOneOrErrorWithTransaction(
                booking.id,
                tx,
            );

            // 是否已設定租借設備
            let isHasSetDeviceId = false;
            let checkDevices: number[] = [];
            if (newBooking.bookingDevices) {
                const checkSetDevice = await this.checkSetDevice(
                    newBooking.bookingDevices,
                );
                isHasSetDeviceId = checkSetDevice.isHasSetDeviceId;
                checkDevices = checkSetDevice.checkRentDevices;
                if (
                    isHasSetDeviceId &&
                    checkDevices.length != newBooking.bookingDevices.length
                ) {
                    throw new httpErrors.BadRequest(
                        '所有租借設備必須一起借出/歸還',
                    );
                }
                //建立租借時一併就指定租借設備
                if (isHasSetDeviceId) {
                    newBooking.status = EnumBookingStatus.Received;
                    for (const device of newBooking.bookingDevices) {
                        const rentDevice =
                            await this.rentDeviceService.findOneOrError(
                                device.rentDeviceId,
                            );
                        rentDevice.rentDeviceBookingId = newBooking.id;
                        rentDevice.status = EnumRentDeviceStatus.Booking; // 出借
                        await rentDevice.save({ transaction: tx });
                    }
                }
            }

            await this.validate(newBooking, params.regionId, tx);

            if (!options?.tx) {
                await tx.commit();
                booking = await this.findOneOrError(newBooking.id);
            }
            return booking;
        } catch (err) {
            if (!options?.tx) await tx.rollback();
            throw err;
        }
    }

    async update(
        params: IUpdateBookingInput,
        options?: { tx: Transaction },
    ): Promise<Booking> {
        const tx = options?.tx || (await db.sequelize.transaction());

        try {
            const updateBooking = await this.findOneOrErrorWithTransaction(
                params.id,
                tx,
            );

            if (params.memberId) {
                updateBooking.memberId = params.memberId;
            }
            if (params.storeId) {
                updateBooking.storeId = params.storeId;
            }
            if (params.startDate) {
                updateBooking.startDate = params.startDate;
            }
            if (params.endDate) {
                updateBooking.endDate = params.endDate;
            }
            if (params.status != undefined) {
                updateBooking.status = params.status;
            }
            if (params.returnAppointmentId) {
                updateBooking.returnAppointmentId = params.returnAppointmentId;
            }

            updateBooking.updatedUserId = params.updatedUserId;
            if (
                updateBooking.status == EnumBookingStatus.Returned &&
                !updateBooking.returnDate
            ) {
                // 歸還日
                updateBooking.returnDate = Helpers.Date.now().toDate();
                updateBooking.endDate = Helpers.Date.now().format('YYYY-MM-DD');
            }

            const beforeEndDate = updateBooking.previous('endDate');

            if (params.bookingRentDevices) {
                if (!_.isArray(params.bookingRentDevices)) {
                    throw new httpErrors.BadRequest(
                        'bookingRentDevices is not valid',
                    );
                }
                await this.bookingDeviceService.removeAll(updateBooking.id, tx);
                await this.bookingDeviceService.bulkCreate(
                    {
                        bookingId: updateBooking.id,
                        bookingRentDevices: params.bookingRentDevices,
                    },
                    tx,
                );

                const updateBookingDevices =
                    await this.bookingDeviceService.search(
                        { bookingId: updateBooking.id },
                        tx,
                    );

                //因為有更新BookingDevices所以要重新更新資料
                updateBooking.bookingDevices = updateBookingDevices.rows;
            }

            if (
                updateBooking.bookingDevices &&
                updateBooking.status != EnumBookingStatus.UnReceive
            ) {
                for (const updateRentDevice of updateBooking.bookingDevices) {
                    if (updateRentDevice.rentDeviceId) {
                        const rentDevice =
                            await this.rentDeviceService.findOneOrError(
                                updateRentDevice.rentDeviceId,
                            );
                        if (
                            updateBooking.status == EnumBookingStatus.Received
                        ) {
                            rentDevice.rentDeviceBookingId = updateBooking.id;
                            rentDevice.status = EnumRentDeviceStatus.Booking; // 出借
                            await rentDevice.save({ transaction: tx });
                        } else {
                            // 歸還 & 取消
                            rentDevice.rentDeviceBookingId = <any>null;
                            rentDevice.status = EnumRentDeviceStatus.Idle;
                            await rentDevice.save({ transaction: tx });
                        }
                    }
                }
            }

            //有異動結束時間且有歸還預約(returnAppointmentId)時更新returnAppointment的預約時間
            if (
                beforeEndDate != updateBooking.endDate &&
                updateBooking.returnAppointmentId
            ) {
                await this.appointmentLibService.update(
                    {
                        id: updateBooking.returnAppointmentId,
                        appointmentDate: updateBooking.endDate,
                        updatedUserId: params.updatedUserId,
                        regionId: updateBooking.returnAppointment!.regionId, //有returnAppointmentId 一定有returnAppointment
                    },
                    tx,
                );
            }
            await this.validate(updateBooking, params.regionId, tx);
            let booking = await updateBooking.save({ transaction: tx });

            if (!options?.tx) {
                await tx.commit();
                booking = await this.findOneOrError(updateBooking.id);
            }
            return booking;
        } catch (err) {
            if (!options?.tx) await tx.rollback();
            throw err;
        }
    }

    async delete(
        params: IDeleteBooking,
        options?: { tx: Transaction },
    ): Promise<void> {
        const tx = options?.tx || (await db.sequelize.transaction());

        try {
            const booking = await this.findOneOrError(params.id);
            if (
                booking.status == EnumBookingStatus.Received ||
                booking.status == EnumBookingStatus.Returned
            ) {
                throw new httpErrors.BadRequest(
                    '「已領取、已歸還」的預約不可以刪除',
                );
            }
            booking.deleted = true;
            booking.updatedUserId = params.updatedUserId;
            await booking.save({ transaction: tx });
            if (!options?.tx) await tx.commit();
        } catch (err) {
            if (!options?.tx) await tx.rollback();
            throw err;
        }
    }

    getAllClosedTimes(closedTime: any): string[] {
        const closedTimes: string[] = [];
        let tempTime = moment(closedTime.startClosingTime, 'HH:mm:ss');
        const lastClosedTime = moment(closedTime.endClosingTime, 'HH:mm:ss');
        while (tempTime <= lastClosedTime) {
            closedTimes.push(tempTime.format('HH:mm'));
            tempTime = tempTime.add(30, 'minute');
        }

        return closedTimes;
    }

    async getAllEndDates(
        params: IRentDeviceDailyInfoParams,
    ): Promise<RentDeviceDailyInfo[]> {
        const EndDateParams = _.cloneDeep(params);
        EndDateParams.filters.chooseEndDate = true;
        return await this.getRentDeviceInfos(EndDateParams);
    }

    async getAllStartDates(
        params: IRentDeviceDailyInfoParams,
    ): Promise<RentDeviceDailyInfo[]> {
        const startDateParams = _.cloneDeep(params);
        startDateParams.filters.chooseStartDate = true;
        return await this.getRentDeviceInfos(startDateParams);
    }

    async getRentDeviceInfos(
        params: IRentDeviceDailyInfoParams,
    ): Promise<RentDeviceDailyInfo[]> {
        const {
            storeId,
            startDate,
            endDate,
            excludeBookingId,
            rentDevice,
            rentStartDate,
            chooseStartDate,
            chooseEndDate,
        } = params.filters;
        const internal = params.internal ?? true;
        const devices: BookingRentDevice[] = [];
        let dates: RentDeviceDailyInfo[] = [];
        let addDay = 0;

        const rentDeviceType = rentDevice.map((row) => row.deviceType);

        if (!_.isArray(rentDeviceType)) {
            throw new httpErrors.BadRequest('請輸入租借產品類別');
        }

        if (chooseStartDate || chooseEndDate) {
            for (const device of rentDevice) {
                if (!_.isNumber(device.count) || device.count < 1) {
                    throw new httpErrors.BadRequest('租借設備必須要有數量');
                }
            }
        }

        if (chooseEndDate && !rentStartDate) {
            throw new httpErrors.BadRequest('查詢租借結束日必須要有租借起始日');
        }

        // 門市可租借商品總類/數量
        const storeRentDevice =
            await this.rentDeviceService.getAllRentDeviceByStoreId({
                storeId,
                rentDeviceType,
            });

        let isCanRentDevice = true;
        rentDeviceType.forEach((row) => {
            const result = storeRentDevice.filter((storeRentDevice) => {
                return storeRentDevice.rentDeviceType == row;
            });
            const counts = result[0]?.counts ?? 0;
            if (counts === 0) {
                isCanRentDevice = false;
            }
            devices.push({
                deviceType: row,
                rental: 0,
                total: counts,
            });
        });

        // 門市營業時間
        const storeBusinessTimes = await db.StoreBusinessHour.findAll({
            where: { storeId: storeId },
        });

        //取得門市休息時間
        const storeClosedTimesResult =
            await this.customClosingTimeService.search({ storeId: storeId });
        const storeClosedTimes = storeClosedTimesResult.rows;

        //從官網來的查詢日期多取前後5天
        if (internal) {
            addDay = 5;
        }

        let realDate1 = Helpers.Date.addDays(moment(startDate), addDay * -1);
        realDate1 = Helpers.Date.timeAtStart(realDate1);

        let realDate2 = Helpers.Date.addDays(moment(endDate), addDay);
        realDate2 = Helpers.Date.timeAtStart(realDate2);

        if (realDate2.isBefore(realDate1)) {
            throw new httpErrors.BadRequest('查詢結束日不可小於查詢開始日');
        }

        let tempDate = realDate1.clone();
        const timeFormat = 'HH:mm';
        // 產生日期區間的所有營業時間日期
        while (tempDate.isSameOrBefore(realDate2, 'day')) {
            let isBusinessDay = true; // 是否是營業日
            const times: any[] = [];
            let lastTime = '';
            let allow = isCanRentDevice;

            const closedDay = storeClosedTimes.find((closedTime) => {
                return (
                    closedTime.isAllDay &&
                    moment(tempDate).isBetween(
                        Helpers.Date.timeAtStart(closedTime.startDate),
                        Helpers.Date.timeAtEnd(closedTime.endDate),
                        'day',
                        '[]',
                    )
                );
            });

            //非全天一次只能申請一天，起迄會是同一天，所以這邊只判斷startDate即可
            let closedTimes: any = storeClosedTimes.filter((closedTime) => {
                return (
                    !closedTime.isAllDay &&
                    moment(closedTime.startDate).isSame(tempDate, 'day')
                );
            });
            closedTimes = closedTimes.map((closedTime) =>
                this.getAllClosedTimes(closedTime),
            );
            closedTimes = _.uniq(_.flattenDeep(closedTimes));

            if (closedDay) {
                // 門市休息日
                isBusinessDay = false;
            } else {
                // 非國定假日
                const dateOfWeek = tempDate.isoWeekday();
                if (storeBusinessTimes) {
                    const startBreakTime: any = storeBusinessTimes.find(
                        (x) => x.dayOfWeek === dateOfWeek,
                    )?.startBreakTime;
                    const closingTime: any = storeBusinessTimes.find(
                        (x) => x.dayOfWeek === dateOfWeek,
                    )?.closingTime;

                    if (
                        !storeBusinessTimes.find(
                            (x) => x.dayOfWeek === dateOfWeek,
                        )?.openingTime
                    ) {
                        // 未營業
                        allow = false;
                        isBusinessDay = false;
                    } else {
                        times.push(
                            Helpers.Date.parse(
                                startBreakTime,
                                timeFormat,
                            ).format(timeFormat),
                        );
                        lastTime = Helpers.Date.parse(
                            closingTime,
                            timeFormat,
                        ).format(timeFormat);
                    }
                }
            }

            dates.push({
                date: moment(tempDate).toISOString(),
                isBusinessDay: isBusinessDay,
                allow: allow,
                times: times,
                lastTime: lastTime,
                rentDevices: devices,
            });
            tempDate = Helpers.Date.addDays(tempDate, 1);
        }

        // 取得日期區間的所有預約
        const bookingsInDateRange = await this.getDateRangeBookings({
            storeId,
            targetStartDate: realDate1.format('YYYY-MM-DD'),
            targetEndDate: realDate2.format('YYYY-MM-DD'),
            rentDeviceType,
            internal,
            excludeBookingId,
        });

        const bookingDates: IDateBookingRentDevice[] = [];
        // 產生每日預約日期
        for (const booking of bookingsInDateRange) {
            const bookingStartDate = Helpers.Date.parse(booking.startDate);
            const bookingEndDate = Helpers.Date.parse(booking.endDate);
            const days = bookingEndDate.diff(bookingStartDate, 'days');

            for (let day = 0; day <= days; day++) {
                const date = Helpers.Date.addDays(bookingStartDate, day);
                if (booking.bookingDevices) {
                    if (day == 0 || day == days) {
                        bookingDates.push({
                            date: date,
                            deviceType: booking.bookingDevices.map(
                                (row) => row.rentDeviceType,
                            ),
                        });
                    } else {
                        bookingDates.push({
                            date: date.startOf('day'),
                            deviceType: booking.bookingDevices.map(
                                (row) => row.rentDeviceType,
                            ),
                        });
                    }
                }
            }
        }

        //記錄每日預約次數
        for (const bookingDate of bookingDates) {
            const dateBalls = Helpers.Date.timeAtStart(bookingDate.date);
            const dateIndex = dates.findIndex((date: any) => {
                return moment(date.date).isSame(dateBalls);
            });
            if (dateIndex != -1) {
                const cloneStoreRentDevices = _.cloneDeep(
                    dates[dateIndex].rentDevices,
                );
                cloneStoreRentDevices.forEach((rentDevice) => {
                    bookingDate.deviceType.forEach((type) => {
                        if (_.eq(type, rentDevice.deviceType)) {
                            rentDevice.rental += 1;
                        }
                    });
                });
                dates[dateIndex].rentDevices = cloneStoreRentDevices;
            }
        }

        //官網預約只能選擇今天之後三個工作日的日期
        const nextThreeBusinessDay = await this.getBusinessDate(
            Helpers.Date.today().format('YYYY-MM-DD'),
            dates,
            3,
        );
        // 備註: count = 租借次數, limit = 可租借次數

        for (const targetDate of dates) {
            const index = dates.indexOf(targetDate);
            if (targetDate.isBusinessDay && targetDate.allow) {
                targetDate.allow = await this.getTargetDateAllowStatus(
                    targetDate.date,
                    dates,
                );
            }
            if (internal && nextThreeBusinessDay) {
                const day = moment(nextThreeBusinessDay.date).diff(
                    moment(targetDate.date),
                    'days',
                );
                if (day > 0) {
                    dates[index].allow = false;
                }
            }
        }

        //給前端小日曆的allow判斷用(resmed官網預約)
        for (const date of dates) {
            const index = dates.indexOf(date);
            date.rentDevices.forEach((row) => {
                rentDevice.forEach((device) => {
                    if (
                        row.deviceType == device.deviceType &&
                        (row.rental == row.total ||
                            row.rental + Number(device.count) > row.total)
                    ) {
                        dates[index].allow = false;
                    }
                });
            });
        }

        //回傳所有可以當起始日日期
        if (chooseStartDate) {
            for (const date of dates) {
                const index = dates.indexOf(date);
                if (params.regionId == EnumRegion.TW && !date.isBusinessDay) {
                    delete dates[index];
                    continue;
                }

                date.rentDevices.forEach((row) => {
                    rentDevice.forEach((device) => {
                        if (
                            row.deviceType == device.deviceType &&
                            (row.rental == row.total ||
                                row.rental + Number(device.count) > row.total)
                        ) {
                            delete dates[index];
                        }
                    });
                });
            }
        }

        //回傳所有可以當結束日的日期
        if (chooseEndDate) {
            let endDateAllow = true;
            for (const date of dates) {
                const index = dates.indexOf(date);
                //比租借日早的日期要剔除
                if (moment(date.date).diff(moment(rentStartDate)) < 1) {
                    delete dates[index];
                    continue;
                }
                //非營業日剃除
                if (params.regionId == EnumRegion.TW && !date.isBusinessDay) {
                    delete dates[index];
                    continue;
                }
                //前面有任何一天無法租借後面就通通不能挑選了
                if (!endDateAllow) {
                    delete dates[index];
                    continue;
                }
                date.rentDevices.forEach((row) => {
                    rentDevice.forEach((device) => {
                        if (
                            row.deviceType == device.deviceType &&
                            (row.rental == row.total ||
                                row.rental + Number(device.count) > row.total)
                        ) {
                            delete dates[index];
                            endDateAllow = false;
                        }
                        //至少要借兩天
                        if (moment(date.date).diff(rentStartDate, 'days') < 1) {
                            delete dates[index];
                        }
                    });
                });
            }
        }
        dates = dates.filter((element) => {
            if (Object.keys(element).length !== 0) {
                return true;
            }
        });

        return dates;
    }

    async getNextBusinessDayInternal(
        params: IBookingNextBusinessDayParams,
        regionId: EnumRegion,
    ): Promise<NextBusinessDay | undefined> {
        const result = { nextBusinessDay: '' };

        const rentDeviceType = params.rentDeviceType ?? EnumRentDeviceType.HST;

        const args: IRentDeviceDailyInfoParams = {
            filters: {
                storeId: params.storeId,
                startDate: params.targetDate,
                endDate: moment(params.targetDate)
                    .add(2, 'M')
                    .format('YYYY-MM-DD'),
                excludeBookingId: params.excludeBookingId,
                rentDevice: [{ deviceType: rentDeviceType, count: 1 }],
            },
            regionId,
        };

        const calendar = await this.getRentDeviceInfos(args);

        const dates = calendar.map((row) => {
            return {
                date: row.date,
                allow: row.allow,
                count: row.rentDevices[0].rental,
                isBusinessDay: row.isBusinessDay,
                rentDevices: row.rentDevices,
                limit: row.rentDevices[0].total,
                times: row.times,
                lastTime: row.lastTime,
            };
        });

        const data = await this.getBusinessDate(params.targetDate, dates);

        if (data !== undefined) {
            result.nextBusinessDay = moment(data.date).format('YYYY-MM-DD');
        }
        return result;
    }

    async getDateRangeBookings(
        params: IBookingsByDateRangeInput,
    ): Promise<Booking[]> {
        const {
            internal,
            rentDeviceType,
            excludeBookingId,
            storeId,
            targetStartDate,
            targetEndDate,
        } = params;

        let statusFilter = [EnumBookingStatus.Canceled];

        //官網排除取消與已歸還的訂單
        if (internal) {
            statusFilter = [
                EnumBookingStatus.Canceled,
                EnumBookingStatus.Returned,
            ];
        }

        const BookingDeviceFilter = {
            rentDeviceType: {
                [db.Op.in]: rentDeviceType,
            },
        };

        const bookingFilters: any = {
            deleted: false,
            storeId: storeId,
            status: {
                [db.Op.notIn]: statusFilter,
            },
            startDate: {
                [db.Op.lte]: targetEndDate,
            },
            endDate: {
                [db.Op.gte]: targetStartDate,
            },
        };
        //排除的Booking
        if (excludeBookingId) {
            bookingFilters.id = {
                [db.Op.not]: excludeBookingId,
            };
        }

        const result = await db.Booking.findAll({
            include: [
                {
                    model: db.BookingDevice,
                    required: true,
                    where: BookingDeviceFilter,
                },
            ],
            where: bookingFilters,
        });

        return result;
    }

    /**
     * @param targetDate 查詢日期
     * @param dateRange 日期區間資料
     * @param days 往後搜尋幾天後
     * @returns 工作日日期
     */
    async getBusinessDate(
        targetDate: string,
        dateRange: RentDeviceDailyInfo[],
        days = 1,
    ): Promise<RentDeviceDailyInfo | undefined> {
        let count = 1;
        const nextBusinessDay = dateRange.find((checkDate) => {
            if (
                checkDate.isBusinessDay &&
                moment(checkDate.date).isAfter(moment(targetDate), 'days')
            ) {
                if (count > days) {
                    return true;
                }
                count++;
            }
        });
        return nextBusinessDay;
    }

    async getTargetDateAllowStatus(
        targetDate: string,
        dateRange: RentDeviceDailyInfo[],
    ): Promise<boolean> {
        let allow = false;

        const nextBusinessDay = await this.getBusinessDate(
            targetDate,
            dateRange,
        );

        const checkDate = Helpers.Date.timeAtStart(targetDate);
        const dateIndex = dateRange.findIndex((date: any) => {
            return moment(date.date).isSame(checkDate);
        });

        if (dateIndex != -1) {
            for (const rentDevice of dateRange[dateIndex].rentDevices) {
                if (rentDevice.rental < rentDevice.total) {
                    allow = true;
                }
            }
        }

        if (nextBusinessDay !== undefined && allow) {
            const nextBusinessCheckDate = Helpers.Date.timeAtStart(
                nextBusinessDay.date,
            );
            const dateIndexNext = dateRange.findIndex((date: any) => {
                return moment(date.date).isSame(nextBusinessCheckDate);
            });
            if (dateIndexNext != -1) {
                for (const rentDevice of dateRange[dateIndexNext].rentDevices) {
                    if (rentDevice.rental < rentDevice.total) {
                        allow = true;
                    } else {
                        allow = false;
                    }
                }
            }
        }
        return allow;
    }

    async validateBookingsStatusFlow(
        status: number | string,
        beforeStatus: number,
    ): Promise<void> {
        switch (beforeStatus) {
            case EnumBookingStatus.Canceled:
                if (status != beforeStatus) {
                    throw new httpErrors.BadRequest(
                        '「已取消」的預約不可以更改預約狀態',
                    );
                }
                break;
            case EnumBookingStatus.UnReceive:
                if (status == EnumBookingStatus.Returned) {
                    throw new httpErrors.BadRequest(
                        '「待領取」的預約不可以更改為「已歸還」',
                    );
                }
                break;
            case EnumBookingStatus.Received:
                if (status == EnumBookingStatus.Canceled) {
                    throw new httpErrors.BadRequest(
                        '不可以取消「已領取」的預約',
                    );
                }
                if (status == EnumBookingStatus.UnReceive) {
                    throw new httpErrors.BadRequest(
                        '「已領取」的預約不可以更改為「待領取」',
                    );
                }
                break;
            case EnumBookingStatus.Returned:
                if (status == EnumBookingStatus.Canceled) {
                    throw new httpErrors.BadRequest(
                        '不可以取消「已歸還」的預約',
                    );
                }
                if (status != beforeStatus) {
                    throw new httpErrors.BadRequest(
                        '「已歸還」的預約不可以更改預約狀態',
                    );
                }
                break;
        }
    }

    async checkSetDevice(
        bookingDevices: BookingDevice[],
    ): Promise<ICheckSetDevice> {
        const checkRentDevices: number[] = [];
        let isHasSetDeviceId = false;
        bookingDevices.forEach((rentDevice) => {
            if (!_.isNull(rentDevice.rentDeviceId)) {
                isHasSetDeviceId = true;
                checkRentDevices.push(rentDevice.rentDeviceId);
            }
        });
        return { isHasSetDeviceId, checkRentDevices };
    }

    async validate(
        booking: Booking,
        regionId: EnumRegion,
        tx?: Transaction,
    ): Promise<void> {
        if (!booking.storeId) {
            throw new httpErrors.BadRequest('請選擇門市');
        }
        const store = await db.Store.findByPk(booking.storeId);
        if (!store) {
            throw new httpErrors.InvalidValueError('門市不存在');
        }
        if (!booking.rentAppointmentId) {
            throw new httpErrors.BadRequest('請輸入預約單號');
        }
        if (!booking.startDate) {
            throw new httpErrors.BadRequest('請輸入租借開始日');
        }
        if (!Helpers.Date.isDate(booking.startDate)) {
            throw new httpErrors.BadRequest('租借開始日格式有誤');
        }
        if (!booking.endDate) {
            throw new httpErrors.BadRequest('請輸入租借結束日');
        }
        if (!Helpers.Date.isDate(booking.endDate)) {
            throw new httpErrors.BadRequest('租借結束日格式有誤');
        }

        const beforeStartDate = booking.previous('startDate');
        const beforeEndDate = booking.previous('endDate');
        const beforeStatus = booking.previous('status');
        const beforeStoreId = booking.previous('storeId');

        const bookingRentDevices = booking.bookingDevices;

        if (
            beforeStatus == EnumBookingStatus.Returned &&
            (beforeEndDate != booking.endDate ||
                beforeStartDate != booking.startDate)
        ) {
            throw new httpErrors.BadRequest(
                '「已歸還」預約不允許變更租借日(起,迄)',
            );
        }
        let checkSetDevice: ICheckSetDevice = {
            isHasSetDeviceId: false,
            checkRentDevices: [],
        };
        let isHasSetDeviceId = false;
        if (bookingRentDevices) {
            checkSetDevice = await this.checkSetDevice(bookingRentDevices);
            isHasSetDeviceId = checkSetDevice.isHasSetDeviceId;

            if (isHasSetDeviceId && beforeStoreId != booking.storeId) {
                throw new httpErrors.BadRequest(
                    '已選用租借設備不允許變更租借門市',
                );
            }

            if (isHasSetDeviceId && beforeStartDate != booking.startDate) {
                throw new httpErrors.BadRequest(
                    '已選用租借設備不允許變更租借日(起)',
                );
            }
        }
        await this.validateBookingsStatusFlow(booking.status, beforeStatus);

        const today = Helpers.Date.today();
        const startDate: moment.Moment = Helpers.Date.parse(booking.startDate);
        const endDate: moment.Moment = Helpers.Date.parse(booking.endDate);
        const date1WithOutTime: moment.Moment =
            Helpers.Date.timeAtStart(startDate);
        const date2WithOutTime: moment.Moment =
            Helpers.Date.timeAtStart(endDate);

        if (endDate.isSameOrBefore(startDate)) {
            throw new httpErrors.BadRequest('預約結束日不可小於等於預約開始日');
        }
        if (
            (beforeStartDate != booking.startDate &&
                startDate.isBefore(today, 'days')) ||
            Helpers.Date.today().add(3, 'months').isBefore(startDate, 'days')
        ) {
            throw new httpErrors.BadRequest('預約日期已超過允許的區間');
        }

        switch (booking.status) {
            case EnumBookingStatus.UnReceive:
                {
                    const rentDevices =
                        await this.bookingDeviceService.getBookingDevicesFromBookingId(
                            booking.id,
                            tx,
                        );

                    const params: IRentDeviceDailyInfoParams = {
                        filters: {
                            rentDevice: rentDevices,
                            excludeBookingId: booking.id || undefined,
                            rentStartDate: booking.startDate,
                            startDate: moment(startDate).format('YYYY-MM-DD'),
                            endDate: moment(endDate).format('YYYY-MM-DD'),
                            storeId: booking.storeId,
                        },
                        regionId,
                    };
                    const bookingStartDateStatus = await this.getAllStartDates(
                        params,
                    );
                    const allowDate1 = bookingStartDateStatus.findIndex(
                        (bookingStartDate) => {
                            return moment(bookingStartDate.date).isSame(
                                date1WithOutTime,
                            );
                        },
                    );
                    if (allowDate1 == -1) {
                        throw new httpErrors.BadRequest(
                            `${Helpers.Date.formatDate(
                                startDate,
                            )} 預約起始日有誤請重新選擇`,
                        );
                    }

                    const bookingEndDateStatus = await this.getAllEndDates(
                        params,
                    );
                    const allowDate2 = bookingEndDateStatus.findIndex(
                        (bookingEndDate) => {
                            return moment(bookingEndDate.date).isSame(
                                date2WithOutTime,
                            );
                        },
                    );
                    if (allowDate2 == -1) {
                        throw new httpErrors.BadRequest(
                            `${Helpers.Date.formatDate(
                                endDate,
                            )} 預約結束日有誤請重新選擇`,
                        );
                    }

                    if (isHasSetDeviceId) {
                        throw new httpErrors.BadRequest(
                            '狀態未領取不可設定租借設備',
                        );
                    }
                }
                break;
            case EnumBookingStatus.Received:
            case EnumBookingStatus.Returned:
                {
                    if (!booking.memberId) {
                        throw new httpErrors.BadRequest('請輸入租借人');
                    }
                    if (
                        _.isEmpty(booking.bookingDevices) ||
                        !isHasSetDeviceId
                    ) {
                        throw new httpErrors.BadRequest('請指定租借/歸還設備');
                    }

                    if (booking.status == EnumBookingStatus.Received) {
                        if (date1WithOutTime.isAfter(today)) {
                            throw new httpErrors.BadRequest(
                                `${Helpers.Date.formatDate(
                                    startDate,
                                )} 才可領取租借設備`,
                            );
                        }
                    }
                    if (bookingRentDevices) {
                        for (const bookingDevice of bookingRentDevices) {
                            if (_.isNull(bookingDevice.rentDeviceId)) {
                                throw new httpErrors.BadRequest(
                                    '請指定租借/歸還設備',
                                );
                            }
                            const rentDevice = await db.RentDevice.findOne({
                                where: {
                                    storeId: booking.storeId,
                                    id: bookingDevice.rentDeviceId,
                                },
                            });
                            if (!rentDevice) {
                                throw new httpErrors.BadRequest(
                                    '租借設備不存在',
                                );
                            }
                            if (booking.status == EnumBookingStatus.Received) {
                                if (
                                    rentDevice.status ===
                                    EnumRentDeviceStatus.Disabled
                                ) {
                                    throw new httpErrors.BadRequest(
                                        '租借設備已停用',
                                    );
                                }
                                if (
                                    rentDevice.status ===
                                    EnumRentDeviceStatus.Transferring
                                ) {
                                    throw new httpErrors.BadRequest(
                                        '租借設備調撥中',
                                    );
                                }
                            }
                        }
                    }
                }
                break;
        }
    }

    private generateDateFilter(dateCondition: DateCondition) {
        const conditions: any[] = [];
        if (dateCondition.eq) {
            conditions.push({
                [db.Op.eq]: dateCondition.eq,
            });
        }
        if (dateCondition.lt) {
            conditions.push({
                [db.Op.lt]: dateCondition.lt,
            });
        }
        if (dateCondition.gte) {
            conditions.push({
                [db.Op.gte]: dateCondition.gte,
            });
        }
        if (dateCondition.lte) {
            conditions.push({
                [db.Op.lte]: dateCondition.lte,
            });
        }
        if (dateCondition.eq) {
            conditions.push({
                [db.Op.eq]: dateCondition.eq,
            });
        }

        return conditions;
    }

    /**
     * 更新預約租借設備（原租借設備流程會產生歸還的預約）
     * @param rentAppointmentBooking 預約租借訂單
     * @param updateBookingDevicesParams 更新租借的內容
     */
    async updateBookingDevicesAtCreateLogistic(
        params: IUpdateBookingDevicesAtCreateLogistic,
        tx: Transaction,
    ) {
        const appointment = await this.appointmentLibService.findOneOrError(
            params.appointmentId,
        );
        if (!appointment.rentAppointmentBooking?.id) return;
        if (
            appointment.rentAppointmentBooking?.status !==
            EnumBookingStatus.UnReceive
        ) {
            return;
        }

        await this.update(
            {
                id: appointment.rentAppointmentBooking?.id,
                bookingRentDevices:
                    params.updateBookingDevicesParams?.bookingRentDevices,
                status: params.updateBookingDevicesParams?.status,
                updatedUserId: params.updatedUserId,
                regionId: appointment.regionId,
            },
            { tx },
        );
    }
}
