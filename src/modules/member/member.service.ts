import { Helpers, Utils } from '@clinico/clinico-node-framework';
import db from '@clinico/clinico-persistence';
import { EnumYesOrNo } from '@clinico/clinico-persistence/dist/base.enum';
import Member, {
    EnumMemberClientType,
    EnumUntraceableType,
} from '@clinico/clinico-persistence/dist/models/member.model';
import { EnumServiceItemType } from '@clinico/clinico-persistence/dist/models/serviceItem.model';
import { FileUpload } from '@clinico/graphql-upload';
import * as DataLoader from 'dataloader';
import * as httpErrors from 'http-errors';
import * as _ from 'lodash';
import { groupBy } from 'lodash';
import * as moment from 'moment';
import sequelize from 'sequelize';
import { Transaction } from 'sequelize/types';
import { Inject, Service } from 'typedi';
import { v4 as uuidv4 } from 'uuid';
import wpcDb from '@/common/databases/tiptop.database';
import { ISearchResult } from '@/common/interfaces/common.interface';
import { DataLoaderBaseService } from '@/common/providers/dataLoaderBase.service';
import { MemberChannelIdService } from '@/modules/member/memberChannelId.service';
import { EnumWPCOrderType } from '../WPCOrder/WPCOrder.type';
import { AudiologistTraceLibService } from '../audiologistTrace/audiologistTrace.lib.service';
import { CodeService } from '../code/code.service';
import { CompanyService } from '../company/company.service';
import { DepartmentService } from '../department/department.service';
import { ServiceItemService } from '../item/serviceItem.service';
import { ModifyLogService } from '../modifyLog/modifyLog.service';
import { EnumQueueType } from '../mq/mq.interface';
import { MqService } from '../mq/mq.service';
import { StoreService } from '../store/store.service';
import { UploadService } from '../upload/upload.service';
import { UserService } from '../user/user.service';
import { WorkDiaryStore } from '../workDiary/workDiary.store';
import { EnumWorkDiaryServiceItem } from '../workDiary/workDiary.type';
import {
    EnumMemberPersonalInfoDocs,
    EnumMemberReminder,
    IBatchUpdateMember,
    ICreateCIMemberParams,
    ICreateMemberParams,
    ISearchMemberParams,
    IUpdateMemberParams,
    IUpdateMemberUntraceable,
} from './member.interface';
import { EnumAHI } from './member.type';

@Service()
export class MemberService extends DataLoaderBaseService<Member> {
    @Inject()
    private uploadService: UploadService;
    @Inject()
    private storeService: StoreService;
    @Inject()
    private companyService: CompanyService;
    @Inject()
    private mqService: MqService;
    @Inject()
    private departmentService: DepartmentService;
    @Inject()
    private audiologistTraceLibService: AudiologistTraceLibService;
    @Inject()
    private userService: UserService;
    @Inject()
    private modifyLogService: ModifyLogService;
    @Inject()
    private codeService: CodeService;
    @Inject()
    private workDiaryStore: WorkDiaryStore;
    @Inject()
    private serviceItemService: ServiceItemService;
    @Inject()
    private memberChannelIdService: MemberChannelIdService;

    async findOneByCode(code: string): Promise<Member | null> {
        const member = await this.search({ clientCode: code });
        if (member.rows.length !== 1) return null;
        return member.rows[0];
    }

    async findOneById(id: number): Promise<Member | null> {
        const member = await this.search({ id });
        if (member.rows.length !== 1) return null;
        return member.rows[0];
    }

    async findOneByMobile(
        mobile: string,
        companyId: number,
    ): Promise<Member | null> {
        const member = await this.search({ cellPhone: mobile, companyId });
        if (member.rows.length !== 1) return null;
        return member.rows[0];
    }

    async findOneOrErrorByCode(clientCode: string): Promise<Member> {
        const member = await this.search({ clientCode });
        if (member.rows.length === 0) {
            throw new httpErrors.NotFound('Member not found');
        } else if (member.rows.length > 1) {
            throw new httpErrors.NotFound('Member redundancy');
        }
        return member.rows[0];
    }

    private maemberDataloader = new DataLoader(
        async (codes: readonly string[]) => {
            const res = await db.Member.findAll({
                where: {
                    code: { [db.Op.in]: codes as string[] },
                },
                raw: true,
            });
            const grouped = groupBy(res, 'code');
            return codes.map((key) => grouped[key]?.[0] || null);
        },
        {
            cache: false,
            batchScheduleFn: (callback) => setTimeout(callback, 50),
        },
    );

    async findOneByCodeUseDataloader(code: string): Promise<Member | null> {
        const res = await this.maemberDataloader.load(code);
        return res;
    }

    async search(
        params: ISearchMemberParams,
    ): Promise<ISearchResult<Member[]>> {
        const Op = db.Op;
        const argAHIs: any[] = [];
        let filters: any = {};
        const workDiaryFilters: any = {};
        const storeFilters: any = {};
        const tagFilters: any = {};
        let validEntryStoreItemId: number[] = [];
        filters.checksumCode = { [Op.ne]: 'N' };
        filters.deleted = false;

        if (params.wpcOrderCode) {
            const clientCodes = await this.findAllFromWPCOrderCode(
                params.wpcOrderCode,
            );
            if (clientCodes && clientCodes.length > 0) {
                if (!filters.clientCode) {
                    filters.clientCode = {};
                }
                filters.clientCode[Op.and] = [
                    ...(filters.clientCode[Op.and] || []),
                    { [Op.in]: clientCodes },
                ];
            }
        }
        const idQueries: any[] = [];
        if (params.id) {
            idQueries.push({ [db.Op.eq]: params.id });
        }
        if (params?.ids?.length) {
            idQueries.push({ [db.Op.in]: params.ids });
        }
        if (params?.codes?.length) {
            filters.code = { [db.Op.in]: params.codes };
        }
        if (params.clientCode) {
            if (!filters.clientCode) {
                filters.clientCode = {};
            }
            filters.clientCode[Op.and] = [
                ...(filters.clientCode[Op.and] || []),
                { [Op.like]: `%${params.clientCode.replace(/\*/gi, '%')}%` },
            ];
        }
        if (params.name) {
            filters.name = {
                [Op.like]: `%${params.name.replace(/\*/gi, '%')}%`,
            };
        }
        if (params.phone) {
            filters.phone = { [Op.like]: `%${params.phone}%` };
        }
        if (params.cellPhone) {
            filters.cellPhone = { [Op.like]: `%${params.cellPhone}%` };
        }
        if (!_.isUndefined(params.storeId)) {
            filters.storeId = _.isNull(params.storeId)
                ? { [db.Op.is]: null }
                : params.storeId;
        }
        if (!_.isUndefined(params.audiologistId)) {
            filters.audiologistId = _.isNull(params.audiologistId)
                ? { [db.Op.is]: null }
                : params.audiologistId;
        }
        if (params.leftEarStatus) {
            filters.leftEarStatus = { [db.Op.in]: params.leftEarStatus };
        }
        if (params.rightEarStatus) {
            filters.rightEarStatus = { [db.Op.in]: params.rightEarStatus };
        }
        if (params.birthday) {
            if (!Helpers.Date.isDate(params.birthday)) {
                throw new httpErrors.BadRequest(
                    `birthday1 is not valid date: ${params.birthday}`,
                );
            }
            if (!filters.birthday) {
                filters.birthday = {};
            }
            filters.birthday[Op.eq] = params.birthday;
        }
        if (params.birthday1) {
            if (!Helpers.Date.isDate(params.birthday1)) {
                throw new httpErrors.BadRequest(
                    `birthday1 is not valid date: ${params.birthday1}`,
                );
            }
            if (!filters.birthday) {
                filters.birthday = {};
            }
            filters.birthday[Op.and] = [
                ...(filters.birthday[Op.and] || []),
                { [Op.gte]: Helpers.Date.parse(params.birthday1).toDate() },
            ];
        }
        if (params.birthday2) {
            if (!Helpers.Date.isDate(params.birthday2)) {
                throw new httpErrors.BadRequest(
                    `birthday2 is not valid date: ${params.birthday2}`,
                );
            }
            if (!filters.birthday) {
                filters.birthday = {};
            }
            filters.birthday[Op.and] = [
                ...(filters.birthday[Op.and] || []),
                { [Op.lte]: Helpers.Date.parse(params.birthday2).toDate() },
            ];
        }
        if (params.birthdayMonth1) {
            filters[Op.and] = [
                ...(filters[Op.and] || []),
                sequelize.literal(
                    `EXTRACT(MONTH FROM birthday) >= ${params.birthdayMonth1}`,
                ),
            ];
        }
        if (params.birthdayMonth2) {
            filters[Op.and] = [
                ...(filters[Op.and] || []),
                sequelize.literal(
                    `EXTRACT(MONTH FROM birthday) <= ${params.birthdayMonth2}`,
                ),
            ];
        }
        if (params.mailCode) {
            filters.mailCode = params.mailCode;
        }
        if (params.companyId) {
            filters.companyId = await this.companyService.generateCompanyFilter(
                params.companyId,
            );
        }
        //會員資料建立時間
        if (params.createdDate1) {
            if (!Helpers.Date.isDate(params.createdDate1)) {
                throw new httpErrors.BadRequest(
                    `createdDate1 is not valid date: ${params.createdDate1}`,
                );
            }
            if (!filters.createdAt) {
                filters.createdAt = {};
            }
            filters.createdAt[Op.and] = [
                ...(filters.createdAt[Op.and] || []),
                { [Op.gte]: Helpers.Date.parse(params.createdDate1).toDate() },
            ];
        }

        if (params.createdDate2) {
            if (!Helpers.Date.isDate(params.createdDate2)) {
                throw new httpErrors.BadRequest(
                    `createdDate2 is not valid date: ${params.createdDate2}`,
                );
            }
            if (!filters.createdAt) {
                filters.createdAt = {};
            }
            filters.createdAt[Op.and] = [
                ...(filters.createdAt[Op.and] || []),
                { [Op.lte]: Helpers.Date.parse(params.createdDate2).toDate() },
            ];
        }

        //ids 集中處理
        if (
            params.memberSN ||
            params.lastPurchaseDate1 ||
            params.lastPurchaseDate2
        ) {
            let memberSNFilter = '';
            if (params.id) {
                memberSNFilter += `AND member_id = '${params.id}' `;
            }
            if (params.memberSN) {
                memberSNFilter += `AND "SN" = '${params.memberSN}' `;
            }

            let lastPurchaseDateFilter = '';
            if (params.lastPurchaseDate1) {
                lastPurchaseDateFilter += ` AND gp.warranty_date >= '${params.lastPurchaseDate1}' `;
            }
            if (params.lastPurchaseDate2) {
                lastPurchaseDateFilter += ` AND gp.warranty_date <= '${params.lastPurchaseDate2}' `;
            }

            const sql = `
            select gp.member_id id
            from ( 
                select sn.member_id, MAX(warranty_start_date) warranty_date
                from member_sn sn
                where 1 = 1 ${memberSNFilter}
                AND deleted = false
                group by sn.member_id ) gp
            where 1 = 1 ${lastPurchaseDateFilter} `;

            idQueries.push({ [db.Op.in]: sequelize.literal(`(${sql})`) });
        }

        if (params.firstEntryStoreDateStart || params.firstEntryStoreDateEnd) {
            let firstEntryStoreDateHaFilter = '';
            let firstEntryStoreDateRsFilter = '';
            if (params.firstEntryStoreDateStart) {
                firstEntryStoreDateHaFilter += `and m.ha_first_date >= '${params.firstEntryStoreDateStart}' `;
                firstEntryStoreDateRsFilter += `and m.rs_first_date >= '${params.firstEntryStoreDateStart}' `;
            }
            if (params.firstEntryStoreDateEnd) {
                firstEntryStoreDateHaFilter += `and m.ha_first_date <= '${params.firstEntryStoreDateEnd}' `;
                firstEntryStoreDateRsFilter += `and m.rs_first_date <= '${params.firstEntryStoreDateEnd}' `;
            }

            const sql = `
            select 
                m.member_id id
            from 
                m_store_member_tracking m
            where 
                ( 1 = 1 ${firstEntryStoreDateHaFilter} )
                or 
                ( 1 = 1 ${firstEntryStoreDateRsFilter} )
            `;
            idQueries.push({ [db.Op.in]: sequelize.literal(`(${sql})`) });
        }

        const clientType = this.checkClientType(params);
        if (params.isPotentialCustomer) {
            if (clientType != '') {
                filters[db.Op.and] = [
                    {
                        clientType: {
                            [Op.like]: clientType,
                        },
                    },
                    {
                        clientType: {
                            [Op.like]: `N%`,
                        },
                    },
                ];
            } else {
                filters.clientType = { [Op.like]: `N%` };
            }
        } else if (clientType != '') {
            filters.clientType = { [Op.like]: clientType };
        }
        if (params.isDead) {
            filters.isDead = params.isDead;
        }
        if (params.isLoseContact != undefined) {
            filters.isLoseContact = params.isLoseContact;
        }
        if (params.search) {
            filters = {
                ...filters,
                [Op.or]: [
                    {
                        // 會員編號
                        clientCode: { [Op.like]: `%${params.search}%` },
                    },
                    {
                        // 姓名
                        name: {
                            [Op.like]: `%${params.search.replace(
                                /\*/gi,
                                '%',
                            )}%`,
                        },
                    },
                    {
                        // 電話
                        phone: { [Op.like]: `%${params.search}%` },
                    },
                    {
                        // 行動電話
                        cellPhone: { [Op.like]: `%${params.search}%` },
                    },
                    {
                        // 常用電話
                        contactPhone: { [Op.like]: `%${params.search}%` },
                    },
                ],
            };
        }
        if (params.searchPhone) {
            filters = {
                ...filters,
                [Op.or]: [
                    {
                        // 電話
                        phone: { [Op.like]: `%${params.searchPhone}%` },
                    },
                    {
                        // 行動電話
                        cellPhone: { [Op.like]: `%${params.searchPhone}%` },
                    },
                    {
                        // 常用電話
                        contactPhone: { [Op.like]: `%${params.searchPhone}%` },
                    },
                ],
            };
        }
        if (params.untraceable != undefined) {
            filters.untraceable = params.untraceable
                ? params.untraceable
                : { [Op.is]: null };
        }
        if (params.AHIs) {
            params.AHIs.forEach((AHI) => {
                switch (AHI) {
                    case EnumAHI.Normal:
                        argAHIs.push({ ahiValue: { [Op.lt]: 5 } });
                        break;
                    case EnumAHI.Mild:
                        argAHIs.push({ ahiValue: { [Op.between]: [5, 15] } });
                        break;
                    case EnumAHI.Moderate:
                        argAHIs.push({ ahiValue: { [Op.between]: [16, 30] } });
                        break;
                    case EnumAHI.Severe:
                        argAHIs.push({ ahiValue: { [Op.gt]: 30 } });
                        break;
                }
            });
            filters = {
                ...filters,
                [Op.or]: argAHIs,
            };
        }
        if (params.regionId) {
            filters.regionId = params.regionId;
        }
        if (params.dataSources) {
            filters.dataSource = { [Op.in]: params.dataSources };
        }
        if (params.lineUserId) {
            filters.lineUserId = params.lineUserId;
        }
        if (params.wechatUserId) {
            filters.wechatUserId = params.wechatUserId;
        }
        if (params.rank) {
            filters.rank = params.rank;
        }

        if (
            params.latestEntryStoreDateStart ||
            params.latestEntryStoreDateEnd
        ) {
            const serviceItem = this.workDiaryStore.getServiceItemCodes(
                EnumWorkDiaryServiceItem.ValidEntryStore,
            );
            const serviceItems = await this.serviceItemService.search({
                type: EnumServiceItemType.WorkItem,
                codes: serviceItem.codes,
                regionId: params.regionId,
            });

            validEntryStoreItemId = serviceItems.rows.map((si) => si.id);

            let workDiaryFilter = '';
            if (params.id) {
                workDiaryFilter += `AND member_id = '${params.id}' `;
            }
            if (params.companyId) {
                workDiaryFilter += `AND company_id = '${params.companyId}' `;
            }

            let latestEntryStoreDateFilter = '';
            if (params.latestEntryStoreDateStart) {
                latestEntryStoreDateFilter += ` AND gp.max_date >= '${params.latestEntryStoreDateStart}' `;
            }
            if (params.latestEntryStoreDateEnd) {
                latestEntryStoreDateFilter += ` AND gp.max_date <= '${params.latestEntryStoreDateEnd}' `;
            }

            const sql = `
            select gp.member_id id
            from ( 
                select member_id, MAX(date) max_date
                from work_diaries wd
                where 1 = 1 ${workDiaryFilter}
                AND wd.deleted = false 
                AND wd.work_service_item_id in (${validEntryStoreItemId.join(
                    ',',
                )})
                group by wd.member_id ) gp
            where 1 = 1 ${latestEntryStoreDateFilter} `;

            filters.id = { [db.Op.in]: sequelize.literal(`(${sql})`) };
        }

        //會員總覽查詢
        if (params.taiwanEarNewUserMemberOverviewTypes) {
            //TODO
        }
        if (params.taiwanResmedNewUserMemberOverviewTypes) {
            //TODO
        }
        if (params.taiwanEarOldUserMemberOverviewTypes) {
            //TODO
        }
        if (params.taiwanResmedOldUserMemberOverviewTypes) {
            //TODO
        }
        if (params.userMemberOverviewOldFieldTypes) {
            //TODO
        }
        if (params.userMemberOverviewNewFieldTypes) {
            //TODO
        }
        if (params.userIds) {
            filters.audiologistId = { [db.Op.in]: params.userIds };
        }
        if (params.storeIds) {
            filters.storeId = { [db.Op.in]: params.storeIds };
        }
        if (params.zoneId) {
            storeFilters.zoneId = params.zoneId;
        }
        if (params.zone2Id) {
            storeFilters.zone2Id = params.zone2Id;
        }
        if (params.tagId) {
            tagFilters.id = params.tagId;
        }
        if (params.tagIds && params.tagIds.length > 0) {
            tagFilters.id = { [Op.in]: params.tagIds };
        }
        if (params.tag) {
            tagFilters.name = { [Op.like]: `%${params.tag}%` };
        }

        const includeModels: sequelize.Includeable[] = [
            {
                model: db.Store,
                where: storeFilters,
                required: Object.keys(storeFilters).length > 0,
            },
            { model: db.Department, as: 'createdDepartment' },
            {
                model: db.Department,
                as: 'department',
                include: [{ model: db.Company }],
            },
            { model: db.District },
            { model: db.City },
            { model: db.Company },
            { model: db.User, as: 'audiologist' },
            { model: db.MemberChannelId, include: [db.User] },
            {
                model: db.MemberTag,
                required: false,
                include: [
                    {
                        model: db.Tag,
                        where: tagFilters,
                        required: Object.keys(tagFilters).length > 0,
                    },
                ],
            },
        ];

        if (params.isPotentialCustomer) {
            includeModels.push({
                model: db.Audition,
                required: true,
            });
        }

        if (
            params.latestEntryStoreDateStart ||
            params.latestEntryStoreDateEnd
        ) {
            workDiaryFilters.workServiceItemId = {
                [db.Op.in]: validEntryStoreItemId,
            };
            includeModels.push({
                model: db.WorkDiary,
                where: workDiaryFilters,
                as: 'workDiaries',
                required: true,
            });
        }

        if (idQueries.length) {
            if (idQueries.length > 1) {
                filters.id = {
                    [db.Op.and]: [...idQueries],
                };
            } else {
                filters.id = idQueries[0];
            }
        }

        const result = await db.Member.findAndCountAll({
            distinct: true,
            include: includeModels,
            where: filters,
            offset: params.offset,
            limit: params.limit,
            order: [['id', 'DESC']],
        });

        return result;
    }

    async create(params: ICreateMemberParams): Promise<Member> {
        const tx = await db.sequelize.transaction();
        try {
            const memberCode = await this.codeService.nextMemberCode(
                params.regionId,
                params.companyId,
                tx,
            );

            const avatar = params.avatar
                ? await this.saveToS3(params.avatar, memberCode)
                : null;
            const newMember = db.Member.build({
                companyId: params.companyId,
                cityId: params.cityId,
                districtId: params.districtId,
                storeId: params.storeId,
                audiologistId: params.audiologistId,
                name: params.name.trim(),
                birthday: params.birthday,
                email: params.email ? params.email.trim() : undefined,
                gender: params.gender,
                isDead: params.isDead,
                phone: params.phone ? params.phone.trim() : undefined,
                cellPhone: params.cellPhone
                    ? params.cellPhone.trim()
                    : undefined,
                contactPhone: params.contactPhone
                    ? params.contactPhone.trim()
                    : undefined,
                mailCode: params.mailCode,
                address: params.address,
                isBlockEmail: params.isBlockEmail,
                isAgreePersonalInfoDocs: params.isAgreePersonalInfoDocs,
                createdUserId: params.createdUserId,
                remark: params.remark,
                deleted: false,
                recommendUserId: params.recommendUserId,
                avatar: avatar,
                clientCode: memberCode,
                code: memberCode,
                checksumCode: 'Y',
                clientType: 'NNNNNN',
                departmentId: params.storeId
                    ? await this.getDepartmentIdByStoreId(params.storeId)
                    : undefined,
                dataSource: params.dataSource,
                rank: params.rank,
                lineUserId: params.lineUserId
                    ? params.lineUserId.trim()
                    : undefined,
                wechatUserId: params.wechatUserId
                    ? params.wechatUserId.trim()
                    : undefined,
                shoplineUserId: params.shoplineUserId
                    ? params.shoplineUserId.trim()
                    : undefined,
                regionId: params.regionId,
            });
            await this.validate(newMember);

            let member = await newMember.save({ transaction: tx });
            await tx.commit();

            await this.mqService.send({
                regionId: params.regionId,
                companyId: params.companyId,
                queue: EnumQueueType.NewMember,
                key: newMember.id,
            });

            member = await this.findOneOrError(newMember.id);
            return member;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    async update(
        params: IUpdateMemberParams,
        bindTx?: Transaction,
    ): Promise<Member> {
        let newAudiologistTraceId: number[] = [];
        let tx = await db.sequelize.transaction();
        if (bindTx) {
            tx = bindTx;
        }
        try {
            const member = await this.findOneOrError(params.id);

            const tempIsDead = member.isDead;
            const tempAudiologistId = member.audiologistId;
            const avatar = params.avatar
                ? await this.saveToS3(params.avatar, member.code)
                : params.avatar;

            if (params.name) {
                params.name = params.name.trim();
            }
            if (params.cellPhone) {
                params.cellPhone = params.cellPhone.trim();
            }
            if (params.contactPhone) {
                params.contactPhone = params.contactPhone.trim();
            }
            if (params.phone) {
                params.phone = params.phone.trim();
            }
            if (params.email) {
                params.email = params.email.trim();
            }
            if (params.lineUserId) {
                params.lineUserId = params.lineUserId.trim();
            }
            if (params.wechatUserId) {
                params.wechatUserId = params.wechatUserId.trim();
            }
            let departmentId;
            if (params.storeId) {
                const depId = await this.getDepartmentIdByStoreId(
                    params.storeId,
                );
                departmentId = depId ?? undefined;
            }

            let clientType = 'NNNNNN';
            if (params.clientTypes && params.clientTypes?.length > 0) {
                const clientTypeArr = clientType.split('');
                if (
                    params.clientTypes.includes(EnumMemberClientType.Ordinary)
                ) {
                    clientTypeArr[0] = 'Y';
                }
                if (
                    params.clientTypes.includes(
                        EnumMemberClientType.CochlearImplant,
                    )
                ) {
                    clientTypeArr[1] = 'Y';
                }
                if (
                    params.clientTypes.includes(
                        EnumMemberClientType.ChangGungHospital,
                    )
                ) {
                    clientTypeArr[2] = 'Y';
                }
                if (
                    params.clientTypes.includes(EnumMemberClientType.Hospital)
                ) {
                    clientTypeArr[3] = 'Y';
                }
                if (params.clientTypes.includes(EnumMemberClientType.Resmed)) {
                    clientTypeArr[4] = 'Y';
                }
                if (
                    params.clientTypes.includes(
                        EnumMemberClientType.VeteransAffairsCouncil,
                    )
                ) {
                    clientTypeArr[5] = 'Y';
                }
                clientType = clientTypeArr.join('');
            }

            const updatedMember = await member.update(
                {
                    ...params,
                    departmentId,
                    avatar,
                    clientType,
                },
                { transaction: tx },
            );

            if (params.lineBindUserId) {
                await this.memberChannelIdService.updateBindUserId(
                    updatedMember.id,
                    params.lineBindUserId,
                );
            }

            await this.validate(member);

            //create log
            await this.modifyLogService.saveLog({
                changeLogs: updatedMember.changeLogs,
                updateUserId: params.updatedUserId,
                transaction: tx,
                regionId: params.regionId,
            });

            if (tempIsDead != params.isDead) {
                switch (params.isDead) {
                    case EnumYesOrNo.Y:
                        newAudiologistTraceId =
                            await this.audiologistTraceLibService.traceClosedBySystem(
                                { memberId: params.id },
                                tx,
                            );
                        break;
                    case EnumYesOrNo.N:
                        newAudiologistTraceId =
                            await this.audiologistTraceLibService.traceCancelClosedBySystem(
                                { memberId: params.id },
                                tx,
                            );
                        break;
                }
            }

            if (
                params.audiologistId &&
                params.audiologistId != tempAudiologistId
            ) {
                await this.audiologistTraceLibService.updateAudiologist(
                    {
                        memberId: member.id,
                        newAudiologistId: params.audiologistId,
                        updatedUserId: params.updatedUserId,
                    },
                    tx,
                );
            }

            if (!bindTx) {
                await tx.commit();
                await this.mqService.send({
                    regionId: params.regionId,
                    companyId: member.companyId,
                    queue: EnumQueueType.Member,
                    key: member.id,
                });
                for (const id of newAudiologistTraceId) {
                    await this.mqService.send({
                        regionId: params.regionId,
                        companyId: member.companyId,
                        queue: EnumQueueType.AudiologistTrace,
                        key: id,
                    });
                }
                return await this.findOneOrError(member.id);
            }

            return member;
        } catch (err) {
            if (!bindTx && tx) await tx.rollback();
            throw err;
        }
    }

    async validate(member: Member): Promise<void> {
        if (!member.code || member.code === '') {
            throw new httpErrors.BadRequest('會員編號生成失敗');
        }
        if (member.email && !Helpers.Str.isEmail(member.email)) {
            throw new httpErrors.BadRequest('Email 驗證錯誤');
        }
        if (Helpers.Str.isEmpty(member.name)) {
            throw new httpErrors.BadRequest('姓名為必填');
        }
        if (member.birthday && !Helpers.Date.isDate(member.birthday)) {
            throw new httpErrors.BadRequest('生日日期驗證錯誤');
        }
        if (member.isAgreePersonalInfoDocs == EnumMemberPersonalInfoDocs.Y) {
            if (
                Helpers.Str.isEmpty(member.phone) &&
                Helpers.Str.isEmpty(member.cellPhone)
            ) {
                throw new httpErrors.BadRequest('行動電話/家用電話 須擇一必填');
            }
            if (Helpers.Str.isEmpty(member.address)) {
                throw new httpErrors.BadRequest('地址為必填');
            }
        }
    }

    convertClientType(type: string): EnumMemberClientType[] {
        const splitString = type.split('');
        if (splitString.length !== 6 && splitString.length !== 1) {
            throw new httpErrors.BadRequest('Invalid clientType');
        }

        const result: any[] = [];
        if (splitString[0] && splitString[0] == 'Y') {
            result.push(EnumMemberClientType.Ordinary);
        }
        if (splitString[1] && splitString[1] == 'Y') {
            result.push(EnumMemberClientType.CochlearImplant);
        }
        if (splitString[2] && splitString[2] == 'Y') {
            result.push(EnumMemberClientType.ChangGungHospital);
        }
        if (splitString[3] && splitString[3] == 'Y') {
            result.push(EnumMemberClientType.Hospital);
        }
        if (splitString[4] && splitString[4] == 'Y') {
            result.push(EnumMemberClientType.Resmed);
        }
        if (splitString[5] && splitString[5] == 'Y') {
            result.push(EnumMemberClientType.VeteransAffairsCouncil);
        }

        return result;
    }

    async findAllFromWPCOrderCode(orderCode: string): Promise<string[]> {
        let memberCodes: string[] = [];

        let sql = `
            SELECT 
                ORDER_CODE                 AS "orderCode"
                ,SHIPPING_CUSTOMER_CODE    AS "memberCode"
            FROM DBUSER1.V_STORE_ORDERS
            WHERE ORDER_CODE = ?
        `;

        /** 針對退輔處理 */
        if (orderCode.substr(0, 5) === EnumWPCOrderType.AS319) {
            sql = `
                SELECT
                    r.ORDERNO 		AS "orderCode"
                    ,vm.code 	 	AS "memberCode"
                FROM DS.RETREATMEMBER r 
                LEFT JOIN DBUSER1.V_MEMBERS vm ON vm.CUSTOMCODE = r.CUSTOMGUEST 
                WHERE r.ORDERNO = ?
            `;
        }

        const result = await wpcDb.raw<any[]>(sql, [orderCode]);
        memberCodes = _.uniq(result.map((row) => row.memberCode));

        return memberCodes;
    }

    async getDepartmentIdByStoreId(id: number): Promise<number | null> {
        const store = await this.storeService.findOneOrError(id);
        const department = await this.departmentService.findOneByCode(
            store.code,
        );
        if (department) {
            return department.id;
        }
        return null;
    }

    private checkClientType(params: {
        isNotPurchase?: boolean;
        isOrdinary?: boolean;
        isCochlearImplant?: boolean;
        isChangGungHospital?: boolean;
        isHospital?: boolean;
        isResmed?: boolean;
        isVeteransAffairsCouncil?: boolean;
    }) {
        let clientType = '';
        if (params.isNotPurchase) {
            clientType = 'NNNNNN';
        }
        if (
            params.isOrdinary != undefined ||
            params.isCochlearImplant != undefined ||
            params.isChangGungHospital != undefined ||
            params.isHospital != undefined ||
            params.isResmed != undefined ||
            params.isVeteransAffairsCouncil != undefined
        ) {
            clientType += params.isOrdinary ? 'Y' : '_';
            clientType += params.isCochlearImplant ? 'Y' : '_';
            clientType += params.isChangGungHospital ? 'Y' : '_';
            clientType += params.isHospital ? 'Y' : '_';
            clientType += params.isResmed ? 'Y' : '_';
            clientType += params.isVeteransAffairsCouncil ? 'Y' : '_';
        }
        return clientType;
    }

    async updateUntraceable(params: IUpdateMemberUntraceable): Promise<Member> {
        const tx = await db.sequelize.transaction();
        let newAudiologistTraceId: number[] = [];
        try {
            const member = await this.findOneOrError(params.memberId);
            const updatedMember = await member.update(
                {
                    untraceable: params.untraceable,
                    updatedUserId: params.updatedUserId,
                },
                { transaction: tx },
            );
            if (params.untraceable == null) {
                newAudiologistTraceId =
                    await this.audiologistTraceLibService.checkAndCreatePreTrace(
                        {
                            member,
                            tx,
                            regionId: params.regionId,
                        },
                    );
            } else {
                newAudiologistTraceId =
                    await this.audiologistTraceLibService.closeAllTrace({
                        member,
                        untraceable: params.untraceable,
                        tx,
                        regionId: params.regionId,
                    });
            }

            //create log
            await this.modifyLogService.saveLog({
                changeLogs: updatedMember.changeLogs,
                updateUserId: params.updatedUserId,
                transaction: tx,
                regionId: params.regionId,
            });

            await tx.commit();

            for (const id of newAudiologistTraceId) {
                await this.mqService.send({
                    regionId: params.regionId,
                    companyId: member.companyId,
                    queue: EnumQueueType.AudiologistTrace,
                    key: id,
                });
            }

            return updatedMember;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    async reminderBirthday(member: Member): Promise<EnumMemberReminder | null> {
        // 本月壽星
        if (
            member.birthday &&
            moment(member.birthday).month() == moment().month()
        ) {
            return EnumMemberReminder.Birthday;
        }
        return null;
    }

    async reminderUntraceable(
        member: Member,
    ): Promise<EnumMemberReminder | null> {
        // 婉拒追蹤
        if (member.untraceable) {
            switch (member.untraceable) {
                case EnumUntraceableType.Unused:
                    return EnumMemberReminder.Unused;
                case EnumUntraceableType.BuyOther:
                    return EnumMemberReminder.BuyOther;
            }
        }
        return null;
    }

    private async saveToS3(
        file: FileUpload,
        memberCode: string,
    ): Promise<string> {
        const dist = `members/${memberCode}/avatars`;
        const filename = uuidv4();
        const result = await this.uploadService.saveToS3({
            dist,
            file,
            filename,
            acl: Utils.AWS.S3.EnumACL.Private,
        });
        return result.key;
    }

    async batchUpdateMember(params: IBatchUpdateMember): Promise<Member[]> {
        const members = await this.search(params.filters);
        let userCompanyId: number | null = null;
        let storeCompanyId: number | null = null;
        if (params.input.audiologistId) {
            const user = await this.userService.findOneOrError(
                params.input.audiologistId,
            );
            userCompanyId = user.companyId;
        }
        if (params.input.storeId) {
            const store = await this.storeService.findOneOrError(
                params.input.storeId,
            );
            storeCompanyId = store.companyId;
        }
        const tx = await db.sequelize.transaction();
        try {
            for (const member of members.rows) {
                if (userCompanyId && userCompanyId != member.companyId) {
                    throw new httpErrors.BadRequest(
                        'companyId(audiologist) is not valid',
                    );
                }
                if (storeCompanyId && storeCompanyId != member.companyId) {
                    throw new httpErrors.BadRequest(
                        'companyId(store) is not valid',
                    );
                }

                //update member
                const updatedMember = await member.update(
                    {
                        ...params.input,
                        updatedUserId: params.updatedUserId,
                    },
                    { transaction: tx },
                );

                //create log
                await this.modifyLogService.saveLog({
                    changeLogs: updatedMember.changeLogs,
                    updateUserId: params.updatedUserId,
                    transaction: tx,
                    regionId: params.regionId,
                });
            }
            await tx.commit();
            const updateMembers = await this.search({
                ids: members.rows.map((data) => data.id),
            });
            return updateMembers.rows;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }

    async searchMemberByPhone(phoneNumber: string): Promise<Member | null> {
        const Op = db.Op;
        const filters: any = {};
        filters.deleted = false;
        filters[Op.or] = [
            {
                [Op.eq]: sequelize.literal(
                    `REGEXP_REPLACE(phone, '\\D', '', 'g') = '${phoneNumber}'`,
                ),
            },
            {
                [Op.eq]: sequelize.literal(
                    `REGEXP_REPLACE(cell_phone, '\\D', '', 'g') = '${phoneNumber}'`,
                ),
            },
            {
                [Op.eq]: sequelize.literal(
                    `REGEXP_REPLACE(contact_phone, '\\D', '', 'g') = '${phoneNumber}'`,
                ),
            },
        ];

        return db.Member.findOne({
            where: filters,
        });
    }

    /**
     * 鎖定最大電子耳自定客編會員
     * @param tx
     * @returns
     */
    async lockMaxCICustomCodeMember(tx: Transaction) {
        const where = {
            customCode: { [db.Op.regexp]: '^P[0-9]{5}' },
        };

        const maxCode = (await db.Member.max('customCode', {
            where,
            transaction: tx,
        })) as string;

        // 沒有找到，返回undefined
        if (!maxCode) return;

        // 找到後上鎖
        db.Member.findOne({
            where: { customCode: maxCode },
            transaction: tx,
            lock: true,
        });

        return maxCode;
    }

    async createCIMember(
        tx: Transaction,
        params: ICreateCIMemberParams,
    ): Promise<Member> {
        try {
            const memberCode = await this.codeService.nextMemberCode(
                params.regionId,
                params.companyId,
                tx,
            );

            const newMember = db.Member.build({
                companyId: params.companyId,
                name: params.name.trim(),
                birthday: params.birthday,
                createdUserId: params.createdUserId,
                deleted: false,
                clientCode: memberCode,
                code: memberCode,
                checksumCode: 'Y',
                clientType: 'NNNNNN',
                regionId: params.regionId,
                customCode: params.customCode,
            });
            await this.validate(newMember);

            const member = await newMember.save({ transaction: tx });

            return member;
        } catch (err) {
            if (tx) await tx.rollback();
            throw err;
        }
    }
}
