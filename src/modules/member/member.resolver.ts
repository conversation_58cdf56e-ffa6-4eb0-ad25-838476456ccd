import { Helpers, Utils } from '@clinico/clinico-node-framework';
import Appointment, {
    EnumAppointmentStatus,
} from '@clinico/clinico-persistence/dist/models/appointment.model';
import Audition from '@clinico/clinico-persistence/dist/models/audition.model';
import Member, {
    EnumMemberClientType,
} from '@clinico/clinico-persistence/dist/models/member.model';
import MemberContact from '@clinico/clinico-persistence/dist/models/memberContact.model';
import Tag from '@clinico/clinico-persistence/dist/models/tag.model';
import User from '@clinico/clinico-persistence/dist/models/user.model';
import WorkDiary from '@clinico/clinico-persistence/dist/models/workDiary.model';
import * as Koa from 'koa';
import * as _ from 'lodash';
import * as moment from 'moment';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    Float,
    Mutation,
    Query,
    Resolver,
    Root,
    UseMiddleware,
} from 'type-graphql';
import { Inject, Service } from 'typedi';
import { AWSHelpers } from '@/common/helpers/AWS.helpers';
import { RequiredUserAuthenticated } from '@/common/middlewares/auth/auth.graphql.middleware';
import { EnumAppointmentServiceItem } from '@/modules/appointment/appointment.type';
import { EventLog } from '../../common/middlewares/eventLog/eventLog.graphql.middleware';
import { UserService } from '../../modules/user/user.service';
import { AppointmentService } from '../appointment/services/appointment.service';
import { AudiologistTraceRepository } from '../audiologistTrace/audiologistTrace.repo';
import { AuditionRepository } from '../audition/providers/audition.repo';
import { BookingService } from '../booking/booking.service';
import { IScoreResult, ISearchResult } from '../mcc/mcc.interface';
import { MccService } from '../mcc/mcc.service';
import { InputPager, Questionaire, QuestionaireScore } from '../mcc/mcc.type';
import { MedicalHistoryPropertiesTypeService } from '../medicalHistoryPropertiesType/medicalHistoryPropertiesType.service';
import { MedicalHistoryPropertiesTypeObject } from '../medicalHistoryPropertiesType/medicalHistoryPropertiesType.type';
import { MemberAccountService } from '../memberAccount/memberAccount.service';
import { MemberAccount } from '../memberAccount/memberAccount.type';
import { MemberCOSIService } from '../memberCOSI/memberCOSI.service';
import { MemberCOSI } from '../memberCOSI/memberCOSI.type';
import { MemberConsultationAnswersService } from '../memberConsultation/memberConsultationAnswers.service';
import {
    EnumMemberConsultationAnswers,
    MemberConsultationQuestionnaire,
} from '../memberConsultation/memberConsultationAnswers.type';
import { MemberConsultationQuestionsService } from '../memberConsultation/memberConsultationQuestions.service';
import { MemberDocumentService } from '../memberDocument/memberDocument.service';
import { MemberDocument } from '../memberDocument/memberDocument.type';
import { MemberMedicalHistoriesPropertyObject } from '../memberMedicalHistoriesProperty/memberMedicalHistoriesProperty.type';
import { MemberMedicalHistoryService } from '../memberMedicalHistory/memberMedicalHistory.service';
import { MemberMedicalHistory } from '../memberMedicalHistory/memberMedicalHistory.type';
import { MemberSNService } from '../memberSN/memberSN.service';
import { MemberSN } from '../memberSN/memberSN.type';
import { MemberTagServiceRepository } from '../memberTag/memberTag.repo';
import { MemberTriedHistoryService } from '../memberTriedHistory/memberTriedHistory.service';
import { MemberTriedHistory } from '../memberTriedHistory/memberTriedHistory.type';
import { ReferralMemberService } from '../referral/services/referralMember.service';
import { DoctorReferralInfoRecord } from '../referral/types/referral.gql.type';
import { ResmedService } from '../resmed/resmed.service';
import {
    Booking,
    ResmedDevice,
    ResmedSubscription,
} from '../resmed/resmed.type';
import { EnumSMSLogType } from '../smsLog/smsLog.interface';
import { SMSLogService } from '../smsLog/smsLog.service';
import { SMSLog } from '../smsLog/smsLog.type';
import { StoreStaff } from '../storeStaff/storeStaff.type';
import { MemberUpdateLog } from '../updateLog/memberUpdateLog.type';
import { MemberUpdateLogService } from '../updateLog/updateLog.service';
import { IUserPayload } from '../user/user.type';
import { UserMemberOverviewTaiwanEarRepository } from '../userMemberOverview/repositories/userMemberOverviewTaiwanEar.repo';
import { UserMemberOverviewTaiwanResmedRepository } from '../userMemberOverview/repositories/userMemberOverviewTaiwanResmed.repo';
import { WorkDiaryService } from '../workDiary/workDiary.service';
import { WorkDiaryDataSourceService } from '../workDiary/workDiaryDataSource.service';
import { DataSource } from '../workDiary/workDiaryDataSource.type';
import {
    EnumMemberBuyingPotential,
    EnumMemberReminder,
} from './member.interface';
import { MemberService } from './member.service';
import {
    BatchUpdateMemberInput as BatchUpdateMemberFieldInput,
    CreateMemberInput,
    MemberFilterInput,
    MembersWrap,
    SearchMemberArgs,
    UpdateMemberInput,
    UpdateMemberUntraceableInput,
} from './member.type';
import { MemberContactService } from './memberContact.service';

@Service()
@Resolver((of) => Member)
class MemberResolver {
    @Inject()
    private memberService: MemberService;
    @Inject()
    private workDiaryService: WorkDiaryService;
    @Inject()
    private auditionRepo: AuditionRepository;
    @Inject()
    private userService: UserService;
    @Inject()
    private mccService: MccService;
    @Inject()
    private memberDocumentService: MemberDocumentService;
    @Inject()
    private memberContactService: MemberContactService;
    @Inject()
    private memberMedicalHistoryService: MemberMedicalHistoryService;
    @Inject()
    private memberConsultationAnswersService: MemberConsultationAnswersService;
    @Inject()
    private memberConsultationQuestionsService: MemberConsultationQuestionsService;
    @Inject()
    private memberCOSIService: MemberCOSIService;
    @Inject()
    private memberSNService: MemberSNService;
    @Inject()
    private audiologistTraceRepo: AudiologistTraceRepository;
    @Inject()
    private bookingService: BookingService;
    @Inject()
    private resmedService: ResmedService;
    @Inject()
    private appointmentService: AppointmentService;
    @Inject()
    private memberUpdateLogService: MemberUpdateLogService;
    @Inject()
    private memberAccountService: MemberAccountService;
    @Inject()
    private smsLogService: SMSLogService;
    @Inject()
    private dataSourceService: WorkDiaryDataSourceService;
    @Inject()
    private memberTriedHistoryService: MemberTriedHistoryService;
    @Inject()
    private medicalHistoryPropertiesTypeService: MedicalHistoryPropertiesTypeService;
    @Inject()
    private referralMemberSvc: ReferralMemberService;
    @Inject()
    private memberTagServiceRepository: MemberTagServiceRepository;
    @Inject()
    private userMemberOverviewTaiwanEarRepo: UserMemberOverviewTaiwanEarRepository;
    @Inject()
    private userMemberOverviewTaiwanResmedRepo: UserMemberOverviewTaiwanResmedRepository;

    private isUserMemberOverview(filters: MemberFilterInput) {
        if (
            !filters.userMemberOverviewNewFieldTypes &&
            !filters.userMemberOverviewOldFieldTypes
        ) {
            return false;
        }
        if (
            filters.taiwanEarNewUserMemberOverviewTypes?.length ||
            filters.taiwanEarOldUserMemberOverviewTypes?.length
        ) {
            return true;
        }
        if (
            filters.taiwanResmedNewUserMemberOverviewTypes?.length ||
            filters.taiwanResmedOldUserMemberOverviewTypes?.length
        ) {
            return true;
        }
        return false;
    }

    private async getUserMemberOverviews(
        filters: MemberFilterInput,
        memberCodes: string[],
    ) {
        const data: string[] = [];
        if (!memberCodes.length) return [];
        if (
            !filters.userMemberOverviewNewFieldTypes &&
            !filters.userMemberOverviewOldFieldTypes
        ) {
            return [];
        }
        const targetDate =
            filters.firstEntryStoreDateStart ??
            filters.latestEntryStoreDateStart ??
            filters.targetDate;
        if (
            filters.taiwanEarNewUserMemberOverviewTypes?.length ||
            filters.taiwanEarOldUserMemberOverviewTypes?.length
        ) {
            const taiwanEars =
                await this.userMemberOverviewTaiwanEarRepo.searchByTypes({
                    targetDate,
                    memberCodes,
                    taiwanEarNewUserMemberOverviewTypes:
                        filters.taiwanEarNewUserMemberOverviewTypes,
                    taiwanEarOldUserMemberOverviewTypes:
                        filters.taiwanEarOldUserMemberOverviewTypes,
                    userMemberOverviewNewFieldTypes:
                        filters.userMemberOverviewNewFieldTypes,
                    userMemberOverviewOldFieldTypes:
                        filters.userMemberOverviewOldFieldTypes,
                });
            data.push(...taiwanEars);
        }
        if (
            filters.taiwanResmedNewUserMemberOverviewTypes?.length ||
            filters.taiwanResmedOldUserMemberOverviewTypes?.length
        ) {
            const taiwanResmeds =
                await this.userMemberOverviewTaiwanResmedRepo.searchByTypes({
                    targetDate,
                    memberCodes,
                    taiwanResmedNewUserMemberOverviewTypes:
                        filters.taiwanResmedNewUserMemberOverviewTypes,
                    taiwanResmedOldUserMemberOverviewTypes:
                        filters.taiwanResmedOldUserMemberOverviewTypes,
                    userMemberOverviewNewFieldTypes:
                        filters.userMemberOverviewNewFieldTypes,
                    userMemberOverviewOldFieldTypes:
                        filters.userMemberOverviewOldFieldTypes,
                });
            data.push(...taiwanResmeds);
        }
        return data;
    }
    @EventLog('member')
    @UseMiddleware(RequiredUserAuthenticated)
    @Query((returns) => [Member], { nullable: true })
    async members(
        @Args() args: SearchMemberArgs,
        @Ctx() ctx: Koa.Context,
    ): Promise<Member[]> {
        const regionId = ctx.req['regionId'];
        const isUserMemberOverview = this.isUserMemberOverview(args.filters);
        const data = await this.memberService.search({
            ...args.filters,
            ...args,
            limit: isUserMemberOverview ? undefined : args.limit,
            regionId,
        });
        const memberCodes = data.rows.map((i) => i.code);
        const memberOverviewCodes = await this.getUserMemberOverviews(
            args.filters,
            memberCodes,
        );
        if (memberOverviewCodes.length) {
            const members = await this.memberService.search({
                codes: memberOverviewCodes,
                limit: args.limit,
                offset: args.offset,
            });
            return members.rows;
        } else if (isUserMemberOverview) {
            return [];
        }
        return data.rows;
    }

    @EventLog('member')
    @UseMiddleware(RequiredUserAuthenticated)
    @Query((returns) => MembersWrap)
    async membersWrap(
        @Args() args: SearchMemberArgs,
        @Ctx() ctx: Koa.Context,
    ): Promise<MembersWrap> {
        const regionId = ctx.req['regionId'];
        const isUserMemberOverview = this.isUserMemberOverview(args.filters);
        const data = await this.memberService.search({
            ...args.filters,
            ...args,
            limit: isUserMemberOverview ? undefined : args.limit,
            regionId,
        });
        const memberCodes = data.rows.map((i) => i.code);
        const memberOverviewCodes = await this.getUserMemberOverviews(
            args.filters,
            memberCodes,
        );
        if (memberOverviewCodes.length) {
            const members = await this.memberService.search({
                codes: memberOverviewCodes,
                limit: args.limit,
                offset: args.offset,
            });
            return {
                members: members.rows,
                count: members.count,
            };
        } else if (isUserMemberOverview) {
            return {
                members: [],
                count: 0,
            };
        }
        return { members: data.rows, count: data.count };
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Query((returns) => Member, { nullable: true })
    async memberByPhone(@Arg('phone') phone: string): Promise<Member | null> {
        return await this.memberService.searchMemberByPhone(phone);
    }

    @EventLog('member')
    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => Member, { name: 'createMember' })
    async create(
        @Arg('input') input: CreateMemberInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Member> {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];
        const newMember = await this.memberService.create({
            ...input,
            avatar: await input.avatar,
            createdUserId: payload.id,
            regionId,
        });
        if (input.appointmentId) {
            await this.appointmentService.updateMember({
                id: input.appointmentId,
                memberId: newMember.id,
                updatedUserId: payload.id,
                regionId,
            });
        }
        return newMember;
    }

    @EventLog('member')
    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => Member)
    async updateMemberFields(
        @Arg('input') input: UpdateMemberInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Member> {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];

        const updatedMember = await this.memberService.update({
            ...input,
            avatar: await input.avatar,
            updatedUserId: payload.id,
            regionId,
        });

        return updatedMember;
    }

    @FieldResolver()
    async code(@Root() member: Member): Promise<string> {
        return member.clientCode;
    }

    @FieldResolver((returns) => String, {
        deprecationReason: 'change to createdAt',
    })
    async createdDate(@Root() member: Member): Promise<string> {
        return Helpers.Date.formatDate(moment(member.createdAt));
    }

    @FieldResolver((returns) => String, {
        deprecationReason: 'change to updatedAt',
    })
    async updatedDate(@Root() member: Member): Promise<string> {
        return Helpers.Date.formatDate(moment(member.updatedAt));
    }

    @FieldResolver((returns) => [ResmedSubscription])
    async resmedSubscriptions(
        @Root() member: Member,
    ): Promise<ResmedSubscription[]> {
        const resmedSubscriptions =
            await this.resmedService.getResmedSubscriptions(member.clientCode);
        return resmedSubscriptions;
    }

    @FieldResolver((returns) => [MemberSN])
    async resmedBuyOuts(@Root() member: Member): Promise<MemberSN[]> {
        const buyOuts = await this.resmedService.getResmedBuyOuts(member.id);
        return buyOuts;
    }

    @FieldResolver((returns) => [Booking])
    async bookings(@Root() member: Member): Promise<Booking[]> {
        const result = await this.bookingService.search({
            memberId: member.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => [ResmedDevice])
    async resmedDevices(@Root() member: Member): Promise<ResmedDevice[]> {
        const resmedDevice = await this.resmedService.search({
            memberId: member.id,
        });
        return resmedDevice.rows;
    }

    @FieldResolver((returns) => [Member])
    async workDiaries(@Root() member: Member): Promise<WorkDiary[]> {
        const data = await this.workDiaryService.search({
            memberId: member.id,
        });
        const workDiaries = data.rows.map((data) => data);
        return workDiaries;
    }

    @FieldResolver((returns) => [Audition])
    async auditions(@Root() member: Member): Promise<Audition[]> {
        if (member.auditions) {
            return member.auditions;
        }
        const data = await this.auditionRepo.search({
            memberId: member.id,
        });
        const auditions = data.rows.map((data) => data);
        return auditions;
    }

    @FieldResolver((returns) => Boolean, {
        description: '會員有選配(一般客戶)',
    })
    async isOrdinary(@Root() member: Member): Promise<boolean> {
        if (member.clientType) {
            const clientTypes = this.memberService.convertClientType(
                member.clientType,
            );
            return clientTypes.includes(EnumMemberClientType.Ordinary);
        }
        return false;
    }

    @FieldResolver((type) => User, { nullable: true })
    async createdUser(@Root() member: Member): Promise<User | null> {
        if (!member.createdUserId) {
            return null;
        }
        const data = await this.userService.findOne(member.createdUserId);
        return data;
    }

    @FieldResolver((type) => User, { nullable: true })
    async updatedUser(@Root() member: Member): Promise<User | null> {
        if (!member.updatedUserId) {
            return null;
        }
        const data = await this.userService.findOne(member.updatedUserId);
        return data;
    }

    @FieldResolver((type) => StoreStaff, { nullable: true })
    async audiologist(@Root() member: Member): Promise<StoreStaff | null> {
        if (!member.audiologistId) {
            return null;
        }
        const data = await this.userService.findOne(member.audiologistId);
        return data;
    }

    @FieldResolver((type) => User, { nullable: true })
    async recommender(@Root() member: Member): Promise<User | null> {
        if (!member.recommendUserId) {
            return null;
        }
        const data = await this.userService.findOne(member.recommendUserId);
        return data;
    }

    @FieldResolver((type) => [Questionaire], { nullable: true })
    async questionaires(
        @Root() member: Member,
        @Arg('pager', (type) => InputPager, { nullable: true })
        pager: InputPager,
    ): Promise<ISearchResult[] | null> {
        const data = await this.mccService.search(member.code, pager);
        return data;
    }

    @FieldResolver((type) => QuestionaireScore, { nullable: true })
    async questionaireScore(
        @Root() member: Member,
    ): Promise<IScoreResult | null> {
        const data = await this.mccService.getScores(member.code);
        return data;
    }

    @FieldResolver((type) => [MemberDocument], { nullable: true })
    async documents(@Root() member: Member): Promise<MemberDocument[]> {
        const data = await this.memberDocumentService.search({
            memberId: member.id,
        });
        return data.rows;
    }

    @FieldResolver((type) => [MemberContact], { nullable: true })
    async contactPeople(@Root() member: Member): Promise<MemberContact[]> {
        const data = await this.memberContactService.search({
            memberId: member.id,
        });
        return data.rows;
    }

    @FieldResolver((type) => [EnumMemberReminder], { nullable: true })
    async reminder(@Root() member: Member): Promise<EnumMemberReminder[]> {
        const reminder: EnumMemberReminder[] = [];
        const birthday = await this.memberService.reminderBirthday(member);
        const untraceable = await this.memberService.reminderUntraceable(
            member,
        );
        const audition = await this.auditionRepo.reminder(member.id);
        const audiologistTrace = await this.audiologistTraceRepo.reminder(
            member.id,
        );
        if (birthday) {
            reminder.push(birthday);
        }
        if (untraceable) {
            reminder.push(untraceable);
        }
        if (audition) {
            reminder.push(audition);
        }
        if (audiologistTrace) {
            reminder.push(audiologistTrace);
        }
        return reminder;
    }

    @FieldResolver((type) => MemberMedicalHistory, { nullable: true })
    async medicalHistory(
        @Root() member: Member,
    ): Promise<MemberMedicalHistory | null> {
        const data = await this.memberMedicalHistoryService.search({
            memberId: member.id,
        });
        return data.rows[0];
    }

    @FieldResolver((type) => [MemberConsultationQuestionnaire], {
        nullable: true,
    })
    async consultation(
        @Root() member: Member,
    ): Promise<MemberConsultationQuestionnaire[] | null> {
        const questionnaires: MemberConsultationQuestionnaire[] = [];

        const answers = await this.memberConsultationAnswersService.search({
            memberId: member.id,
        });

        for (const row of answers.rows) {
            let importancyOfImprovement: number | undefined = undefined;
            if (row.version === 2) {
                importancyOfImprovement =
                    row.answers[row.answers.length - 1].answer;
                row.answers.pop();
            }

            const questions =
                await this.memberConsultationQuestionsService.search({
                    version: row.version,
                });
            questionnaires.push({
                id: row.id,
                createdAt: row.createdAt,
                questionBank: {
                    version: row.version,
                    questions: questions.rows,
                },
                answers: row.answers,
                importancyOfImprovement,
            });
        }

        return questionnaires;
    }

    @FieldResolver((type) => [MemberCOSI], { nullable: true })
    async COSI(@Root() member: Member): Promise<MemberCOSI[] | null> {
        const data = await this.memberCOSIService.search({
            memberId: member.id,
        });
        return data.rows;
    }

    @FieldResolver((type) => [MemberSN], {
        nullable: true,
        description: '購買過商品',
    })
    async memberSN(@Root() member: Member): Promise<MemberSN[]> {
        const data = await this.memberSNService.search({
            memberId: member.id,
        });
        return data.rows;
    }

    @FieldResolver((type) => EnumMemberBuyingPotential, {
        nullable: true,
        description: '購買淺力',
    })
    async buyingPotential(
        @Root() member: Member,
    ): Promise<EnumMemberBuyingPotential | null> {
        const audition = await this.auditionRepo.search({
            memberId: member.id,
        });
        const consultationAnswers =
            await this.memberConsultationAnswersService.search({
                memberId: member.id,
            });
        if (audition.count === 0 || consultationAnswers.count === 0)
            return null;

        const earLossLevel = Math.max(
            Helpers.Str.toIntOrZero(audition.rows[0].lEarLossLevel),
            Helpers.Str.toIntOrZero(audition.rows[0].rEarLossLevel),
        );
        const ScoreMap = {
            [EnumMemberConsultationAnswers.Yes]: 4,
            [EnumMemberConsultationAnswers.Sometimes]: 2,
            [EnumMemberConsultationAnswers.No]: 0,
        };
        const HHIE = consultationAnswers.rows[0].answers.reduce(
            (prev, item) => prev + ScoreMap[item.answer],
            0,
        );

        // 判斷聽損程度 ＆ HHIE分數
        let result: EnumMemberBuyingPotential | null;
        if (HHIE <= 8) {
            result = EnumMemberBuyingPotential.Low;
        } else if (HHIE >= 10 && HHIE <= 24) {
            if (earLossLevel >= 4) {
                // 中重度以上為高購買力
                result = EnumMemberBuyingPotential.High;
            } else {
                result = EnumMemberBuyingPotential.Middle;
            }
        } else if (HHIE >= 26) {
            result = EnumMemberBuyingPotential.High;
        } else {
            result = null;
        }
        return result;
    }

    /**
     * PSG來源的AHI值
     * @param member
     * @returns number
     */
    @FieldResolver((type) => Float, {
        nullable: true,
        description: 'PSG來源的AHI值',
        deprecationReason: '未來改用 PsgAhiValue',
    })
    async ahiValueByPSG(@Root() member: Member): Promise<number | null> {
        return member.PsgAhiValue;
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => Member)
    async updateMemberUntraceable(
        @Arg('input') input: UpdateMemberUntraceableInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Member> {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];
        const updatedMember = await this.memberService.updateUntraceable({
            ...input,
            updatedUserId: payload.id,
            regionId,
        });
        return updatedMember;
    }

    @FieldResolver((type) => String, { nullable: true })
    async avatar(@Root() member: Member): Promise<string | null> {
        if (!member.avatar) return null;
        return Utils.AWS.S3.getSignedUrl({
            bucket: AWSHelpers.getS3Bucket(),
            key: member.avatar,
        });
    }

    @UseMiddleware(RequiredUserAuthenticated)
    @Mutation((returns) => [Member], { description: '更新會員資料(聽力師)' })
    async batchUpdateMember(
        @Arg('filters') filters: MemberFilterInput,
        @Arg('input') input: BatchUpdateMemberFieldInput,
        @Ctx() ctx: Koa.Context,
    ) {
        const payload = ctx.req['user'] as IUserPayload;
        const regionId = ctx.req['regionId'];
        const members = await this.memberService.batchUpdateMember({
            filters,
            input,
            updatedUserId: payload.id,
            regionId,
        });
        return members;
    }

    @FieldResolver((returns) => [MemberUpdateLog], { nullable: true })
    async memberUpdateLogs(@Root() member: Member): Promise<MemberUpdateLog[]> {
        const data = await this.memberUpdateLogService.search({
            memberId: member.id,
        });
        const memberUpdateLogs = data.rows.map(
            (data) =>
                <MemberUpdateLog>{
                    ...data,
                    memberId: data.targetId,
                },
        );

        return memberUpdateLogs;
    }

    @FieldResolver((returns) => Appointment, {
        nullable: true,
        description: '今日往後算起第一筆未報到的預約',
    })
    async nextUnregisteredAppointment(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
        @Arg(
            'notAppointmentServiceItemCode',
            () => EnumAppointmentServiceItem,
            {
                nullable: true,
                description: '不包含的項目',
            },
        )
        notAppointmentServiceItemCode?: EnumAppointmentServiceItem,
        @Arg(
            'appointmentServiceItemCodes',
            () => [EnumAppointmentServiceItem],
            {
                nullable: true,
                description: '包含的項目',
            },
        )
        appointmentServiceItemCodes?: EnumAppointmentServiceItem[],
    ): Promise<Appointment | null> {
        const regionId = ctx.req['regionId'];
        const appointment =
            await this.appointmentService.nextUnregisteredAppointmentByMemberId(
                member.id,
                regionId,
                notAppointmentServiceItemCode,
                appointmentServiceItemCodes,
            );
        return appointment;
    }

    @FieldResolver((returns) => Appointment, {
        nullable: true,
        description: '最後一次已報到的預約往後算起第一筆未報到的預約',
    })
    async nextUnregisteredAppointmentAfterLastRegistered(
        @Root() member: Member,
    ): Promise<Appointment | null> {
        const appointments =
            await this.appointmentService.searchByMemberIdUseDataloader(
                member.id,
            );

        if (!appointments?.length) {
            return null;
        }

        const now = moment();

        //get last registered appointment
        const lastRegistered =
            appointments
                .filter(
                    (item) => item['status'] === EnumAppointmentStatus.Register,
                )
                .pop() || null;

        //unregistered appointments after last registered appointment
        const unregisteredAfterLastRegistered = appointments.filter(
            (item) =>
                item['status'] === EnumAppointmentStatus.Unregistered &&
                (lastRegistered
                    ? moment(item['date']).isAfter(lastRegistered['date'])
                    : true),
        );

        const beforeNowClosest = unregisteredAfterLastRegistered
            .filter((item) => moment(item['date']).isBefore(now))
            .pop();

        const afterNowClosest = unregisteredAfterLastRegistered.filter((item) =>
            moment(item['date']).isAfter(now),
        )[0];

        //compare beforeNowClosest and afterNowClosest to get the id of the closest one to now
        let result: Appointment | null;
        if (beforeNowClosest && afterNowClosest) {
            const beforeDiff = now.diff(beforeNowClosest['date'], 'seconds');
            const afterDiff = moment(afterNowClosest['date']).diff(
                now,
                'seconds',
            );

            result =
                beforeDiff < afterDiff ? beforeNowClosest : afterNowClosest;
        } else {
            result = beforeNowClosest || afterNowClosest || null;
        }

        return result;
    }

    @FieldResolver((returns) => [DoctorReferralInfoRecord], {
        nullable: true,
        description: '醫生轉介紀錄',
    })
    async doctorReferralInfoRecords(
        @Root() member: Member,
    ): Promise<DoctorReferralInfoRecord[] | null> {
        const doctorReferralInfo =
            await this.referralMemberSvc.getDoctorReferralInfoRecordsByMemberId(
                member.id,
            );
        return doctorReferralInfo;
    }

    @FieldResolver((returns) => Appointment, {
        nullable: true,
        description: '今日往後算起第一筆NextStage未報到的預約',
    })
    async nextUnregisteredNextStageAppointment(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<Appointment | null> {
        const regionId = ctx.req['regionId'];
        const appointment =
            await this.appointmentService.nextUnregisteredAppointmentByMemberId(
                member.id,
                regionId,
            );
        return appointment;
    }

    @FieldResolver((type) => MemberAccount, { nullable: true })
    async memberAccount(@Root() member: Member): Promise<MemberAccount | null> {
        if (!member.clientCode) {
            return null;
        }
        const data = await this.memberAccountService.search({
            memberCode: member.clientCode,
        });
        return data.rows[0];
    }

    @FieldResolver((type) => [EnumMemberClientType])
    async clientTypes(@Root() member: Member): Promise<EnumMemberClientType[]> {
        return this.memberService.convertClientType(member.clientType);
    }

    @FieldResolver((type) => [SMSLog])
    async SMSLogs(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<SMSLog[]> {
        const regionId = ctx.req['regionId'];
        let smsLogs: SMSLog[] = [];

        // 會員身上的簡訊
        const memberSMSLogs = await this.smsLogService.search({
            type: EnumSMSLogType.member,
            keyId: member.id,
        });
        smsLogs = smsLogs.concat(memberSMSLogs.rows);

        // 會員關聯預約上的簡訊
        const appointments = await this.appointmentService.search({
            memberId: member.id,
            regionId: regionId,
        });
        for (const appointment of appointments.rows) {
            const appointmentSMSLogs = await this.smsLogService.search({
                type: EnumSMSLogType.appointment,
                keyId: appointment.id,
            });
            if (appointmentSMSLogs.count > 0) {
                smsLogs = smsLogs.concat(appointmentSMSLogs.rows);
            }
        }
        return _.orderBy(smsLogs, ['createdAt'], ['desc']);
    }

    @FieldResolver((type) => [Appointment])
    async appointments(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<Appointment[]> {
        const regionId = ctx.req['regionId'];
        const result = await this.appointmentService.search({
            memberId: member.id,
            regionId,
        });
        return result.rows;
    }

    @FieldResolver((type) => DataSource, { nullable: true })
    async firstDatasource(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<DataSource | null> {
        const regionId = ctx.req['regionId'];
        if (!member.dataSource) return null;
        return await this.dataSourceService.findOneByCode(
            member.dataSource,
            regionId,
        );
    }

    @FieldResolver((type) => DataSource, { nullable: true })
    async latestDatasource(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<DataSource | null> {
        const regionId = ctx.req['regionId'];
        if (!member.updatedDataSource) return null;
        return await this.dataSourceService.findOneByCode(
            member.updatedDataSource,
            regionId,
        );
    }

    @FieldResolver((type) => [MemberTriedHistory], { nullable: true })
    async triedHistory(
        @Root() member: Member,
        @Ctx() ctx: Koa.Context,
    ): Promise<MemberTriedHistory[]> {
        const regionId = ctx.req['regionId'];
        const triedHistory = await this.memberTriedHistoryService.search({
            memberId: member.id,
            regionId,
        });
        return triedHistory.rows;
    }

    @FieldResolver(() => [MedicalHistoryPropertiesTypeObject])
    async medicalHistoryPropsTypes(
        @Root() params: MemberMedicalHistoriesPropertyObject,
    ): Promise<MedicalHistoryPropertiesTypeObject[]> {
        return await this.medicalHistoryPropertiesTypeService.search({});
    }

    @FieldResolver(() => WorkDiary, { nullable: true })
    async latestEntryStore(@Root() member: Member): Promise<WorkDiary | null> {
        if (member?.workDiaries?.length) {
            const workDiary = _.maxBy(member.workDiaries, 'date');
            return workDiary ?? null;
        }
        return await this.workDiaryService.latestEntryStoreByMemberId(
            member.id,
        );
    }

    @FieldResolver(() => [Tag], { nullable: true })
    async tags(@Root() member: Member): Promise<Tag[] | null> {
        return await this.memberTagServiceRepository.findTagsByMemberId(
            member.id,
        );
    }
}
