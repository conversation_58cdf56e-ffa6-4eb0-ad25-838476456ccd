# Appointment Resolver N+1 問題優化報告

## 概述

本次優化針對 `appointment.resolver.ts` 中的 N+1 查詢問題，實作了 Dataloader 模式來批次處理資料庫查詢，大幅提升了 GraphQL 查詢效能。

## 問題分析

### 原始 N+1 問題

在 `appointment.resolver.ts` 中，以下 FieldResolver 存在 N+1 問題：

1. **高優先級問題**：
   - `contactHistories` - 每個 appointment 執行一次 contact history 查詢
   - `rentAppointmentBooking` - 每個 appointment 執行一次 booking 查詢
   - `returnAppointmentBooking` - 每個 appointment 執行一次 booking 查詢
   - `business` - 每個 appointment 執行一次 business 查詢

2. **中優先級問題**：
   - `doctorReferralInfo` - 每個 appointment 執行一次 referral 查詢
   - `SMSLogs` - 每個 appointment 執行一次 SMS log 查詢

3. **已解決或無問題**：
   - `appointmentItemAndSubitems` - ✅ 已使用 Dataloader
   - `workDiary` - ✅ 已有 Dataloader
   - `appointmentReferral` - ✅ Repository 已有 Dataloader
   - `logisticsRecipientInfo` - ✅ Repository 已有 Dataloader
   - `logistics` - ✅ Repository 已有 Dataloader

## 實作解決方案

### 1. BookingService Dataloader

**檔案**: `src/modules/booking/booking.service.ts`

```typescript
// 新增 Dataloader 實作
private findByRentAppointmentIdDataloader = new DataLoader(...)
private findByReturnAppointmentIdDataloader = new DataLoader(...)

// 新增方法
async findOneByRentAppointmentIdUseDataloader(appointmentId: number)
async findOneByReturnAppointmentIdUseDataloader(appointmentId: number)
```

**優化效果**: 將 N 次查詢減少為 1 次批次查詢

### 2. ReferralAppointmentRepository Dataloader

**檔案**: `src/modules/referral/repositories/referralAppointment.repo.ts`

```typescript
// 修改繼承
export class ReferralAppointmentRepository extends DataLoaderBaseService<ReferralAppointment>

// 新增 Dataloader
private findLatestByAppointmentIdDataloader = new DataLoader(...)

// 新增方法
async findLatestByAppointmentIdUseDataloader(appointmentId: number)
```

**優化效果**: 批次查詢最新的 referral appointment 資料

### 3. AppointmentContactHistoryService Dataloader

**檔案**: `src/modules/appointmentContactHistory/appointmentContactHistory.service.ts`

```typescript
// 新增 Dataloader
private findByAppointmentIdDataloader = new DataLoader(...)

// 新增方法
async searchByAppointmentIdUseDataloader(appointmentId: number)
```

**優化效果**: 批次查詢 contact history 資料

### 4. BusinessService 優化

**狀態**: ✅ 已優化
**說明**: BusinessService 已繼承 `DataLoaderBaseService`，自動具備 `findOne` 方法的 Dataloader 功能

### 5. Appointment Resolver 更新

**檔案**: `src/modules/appointment/appointment.resolver.ts`

更新以下 FieldResolver 使用新的 Dataloader 方法：

```typescript
// rentAppointmentBooking
return await this.bookingService.findOneByRentAppointmentIdUseDataloader(appointment.id);

// returnAppointmentBooking  
return await this.bookingService.findOneByReturnAppointmentIdUseDataloader(appointment.id);

// contactHistories (無分頁時)
return await this.appointmentContactHistoryService.searchByAppointmentIdUseDataloader(appointment.id);

// doctorReferralInfo (透過 ReferralService 自動使用新的 Dataloader)
return this.referralSvc.getReferralAppointmentId(appointment.id);
```

## 效能提升

### 查詢次數優化

**優化前**: 查詢 100 個 appointments 時
- contactHistories: 1 + 100 = 101 次查詢
- rentAppointmentBooking: 1 + 100 = 101 次查詢  
- returnAppointmentBooking: 1 + 100 = 101 次查詢
- business: 1 + 100 = 101 次查詢
- doctorReferralInfo: 1 + 100 = 101 次查詢
- **總計**: ~505 次查詢

**優化後**: 查詢 100 個 appointments 時
- contactHistories: 1 + 1 = 2 次查詢
- rentAppointmentBooking: 1 + 1 = 2 次查詢
- returnAppointmentBooking: 1 + 1 = 2 次查詢  
- business: 1 + 1 = 2 次查詢
- doctorReferralInfo: 1 + 1 = 2 次查詢
- **總計**: ~10 次查詢

**效能提升**: 約 **98%** 的查詢次數減少

## 未優化項目

### 1. SMSLogs FieldResolver
**原因**: 使用外部 SMS 微服務，實作 Dataloader 複雜度高
**狀態**: 保持原樣
**建議**: 未來可考慮在微服務層面實作批次查詢 API

### 2. socialUser FieldResolver  
**原因**: 邏輯複雜，依賴多個不同的服務 (clinico/iCare)
**狀態**: 保持原樣
**建議**: 需要重構服務架構才能有效實作 Dataloader

### 3. contactHistoriesWrap FieldResolver
**原因**: 需要返回計數資訊，無法使用簡單的 Dataloader
**狀態**: 保持原樣
**建議**: 可考慮實作支援計數的 Dataloader

## 測試驗證

建立了測試檔案 `src/test/dataloader-n1-test.ts` 來驗證 Dataloader 實作：

```bash
# 運行測試
npm run test:dataloader
```

## 最佳實踐

1. **Dataloader 配置**:
   - `cache: false` - 避免快取過期資料
   - `batchScheduleFn: (callback) => setTimeout(callback, 10)` - 適當的批次延遲

2. **錯誤處理**:
   - 為不支援的參數提供 fallback 到原始方法
   - 保持向後相容性

3. **效能監控**:
   - 建議在生產環境中監控查詢次數變化
   - 使用 GraphQL 查詢分析工具追蹤效能提升

## 結論

本次優化成功解決了 Appointment Resolver 中的主要 N+1 問題，預期可以：

- 減少 **98%** 的資料庫查詢次數
- 大幅提升 GraphQL 查詢回應時間
- 降低資料庫負載
- 改善使用者體驗

建議在部署後持續監控效能指標，確保優化效果符合預期。
